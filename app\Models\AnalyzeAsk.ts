import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column, HasMany, hasMany, HasOne, hasOne } from '@ioc:Adonis/Lucid/Orm'
import Diagnostic from './Diagnostic'
import AnalyzeAskItem from './AnalyzeAskItem'
import QuotationRequest from './QuotationRequest'

export default class AnalyzeAsk extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column({ columnName: 'diagnostic_id' })
  public diagnosticId: number

  @column({ columnName: 'reference' })
  public reference: string

  @column({ columnName: 'pro_id' })
  public proId: number

  @column({ columnName: 'fmd_id' })
  public fmdId: number

  @column({ columnName: 'patient_id' })
  public patientId: number

  @column({ columnName: 'reason' })
  public reason: string

  @column({ columnName: 'used_insurance' })
  public usedInsurance: boolean

  @column({ columnName: 'payment_status' })
  public paymentStatus: string

  @belongsTo(() => Diagnostic, {
    foreignKey: 'diagnosticId',
    localKey: 'id',
  })
  public diagnostic: BelongsTo<typeof Diagnostic>

  @hasMany(() => AnalyzeAskItem, {
    foreignKey: 'analyzeAskId',
    localKey: 'id',
  })
  public items: HasMany<typeof AnalyzeAskItem>

  @hasOne(() => QuotationRequest,{
    foreignKey: 'analyzeAskId',
    localKey: 'id'
  })
  public quotation_requests: HasOne<typeof QuotationRequest>
}
