import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { schema } from '@ioc:Adonis/Core/Validator';
import Database from '@ioc:Adonis/Lucid/Database';
import { formatErrorResponse } from 'App/Controllers/Utils';
import { Address, ApiResponse, OrderClient, PaymentCallbackRequest, PaymentOrder, PaymentResponse } from 'App/Controllers/Utils/models'
import HelperController from '../helpers/HelperController';
import PaymentGateway from 'App/Models/PaymentGateway';
import PaymentApiTrait from '../traits/PaymentApiTrait';
import PaymentIntent from 'App/Models/PaymentIntent';
import { DateTime } from 'luxon';
import { Payment } from 'App/Models/Payment';
import PaymentCallback from 'App/Models/PaymentCallback';
import WalletTrait from '../traits/WalletTrait';

export default class PaymentIntentController extends HelperController {


  public async initWalletDeposit({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    };

    let token = request.header('DOTOKEN');
    if (!token) {
      return response.status(401).json({
        success: false,
        message: "Token manquant",
        result: null
      });
    }
    let userId: number;
    try {
      // Validate the payload
      const payload = await request.validate({
        schema: schema.create({
          amount: schema.number(),
          phone: schema.string({ trim: true }),
          methodId: schema.number(),
        })
      });

      const { amount, phone, methodId } = payload;

      // Vérification du montant
      if (amount <= 0) {
        return response.status(400).json({
          success: false,
          message: "Le montant doit être supérieur à 0",
          result: null
        });
      }

      // Récupérer l'utilisateur et la passerelle de paiement
      userId = (await this.verifyJWT(String(token))).user_id as number;
      const [user, gateway] = await Promise.all([
        this.getUserById(userId),
        PaymentGateway.first()
      ]);

      // Vérifier l'utilisateur et la passerelle
      if (!user || !gateway) {
        return response.status(404).json({
          success: false,
          message: "Aucun utilisateur ou passerelle de paiement trouvé",
          result: null
        });
      }

      // Récupérer la méthode de paiement
      const method = gateway.methods?.find((method) => method.id === methodId);
      if (!method) {
        return response.status(400).json({
          success: false,
          message: "Méthode de paiement invalide",
          result: null
        });
      }

      // Récupérer l'entité associée à l'utilisateur
      const entity = await this.getEntityByUserId(userId, Number(user.roleId));
      if (!entity) {
        return response.status(404).json({
          success: false,
          message: "Aucune entité associée à cet utilisateur",
          result: null
        });
      }

      // Deuxième try-catch : Gestion de la transaction après validation des données
      const trx = await Database.transaction();
      try {
        const firstAddress = entity.address && entity.address.length > 0
          ? (typeof entity.address[0] === 'string'
            ? entity.address[0]
            : (entity.address[0] as Address).libelle || "domicile")
          : "domicile";

        // console.log("Première adresse de l'utilisateur :", firstAddress);

        const reference = new Date().getTime();
        const phoneNumber = '+' + user.country.prefix + phone;

        const client: OrderClient = {
          lastname: user.username.split(" ")[0],
          firstname: user.username.split(" ")[1],
          email: entity.email ?? null,
          phone: phoneNumber,
          address1: firstAddress,
          country: user.country.name,
        };

        const trait = new PaymentApiTrait();
        const host = request.host();
        const protocol = request.protocol();
        const callbackURL = `${protocol}://${host}/api/payments/intent/callback`;
        // console.log("callbackURL", callbackURL);

        const order: PaymentOrder = {
          merchant_reference: reference.toString(),
          amount: amount,
          client: client,
          description: "Rechargement de portefeuille DO",
          gateway_id: method.id,
          type_notif: ["sms"],
          callback_url: callbackURL,
        };

        const payment = await trait.createOrder(order);

        if (payment.success) {
          const metadata = {
            state: payment.result.state,
            code: payment.result.code,
            qrcode_url: payment.result.qrcode_url,
            bill_url: payment.result.bill_url,
            payment_status: payment.result.payment_status,
            merchant_reference: payment.result.merchant_reference,
            order_reference: payment.result.order_reference,
            gateway_id: method.id,
            payments_method: payment.result.payments_method
          };

          const intent = await PaymentIntent.create({
            reference: reference.toString(),
            amountPaid: amount,
            userId: user.id,
            beneficiaryId: entity.id,
            paymentTypeId: 8,
            client: client,
            fees: Number(method.fees),
            paymentMethod: "MOBILE_MONEY",
            phone: phoneNumber !== null ? String(phoneNumber) : '+' + user.country.prefix + entity.phone,
            network: method.libelle,
            paymentLink: payment.result.bill_url,
            status: payment.result.status,
            description: "Rechargement du portefeuille",
            requestAt: DateTime.now(),
            metadata: metadata,
            callbackUrl: callbackURL,
          });

          intent.useTransaction(trx);
          await trx.commit();

          apiResponse = {
            success: true,
            message: "Le paiement a été créé avec succès",
            result: { intent, payment: payment.result }
          };

        } else {
          // console.log("error in init wallet deposit", payment);

          await trx.rollback();
          apiResponse = {
            success: false,
            message: "Erreur lors de la création du paiement",
            result: null,
            except: payment
          };
        }

      } catch (error) {
        await trx.rollback();
        console.log("error in init wallet deposit", error.message);
        apiResponse = {
          success: false,
          message: "Erreur lors de la transaction",
          result: null,
          except: error
        };
      }

    } catch (error) {
      // Gestion des erreurs de validation et de récupération des données
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null,
        except: error
      };
    }

    return response.status(200).json(apiResponse);
  }


  public async checkWalletIntent({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    }
    const trx = await Database.transaction();
    try {
      const payload = await request.validate({
        schema: schema.create({
          reference: schema.number(),
        })
      });
      const { reference } = payload;
      const intent = await PaymentIntent.query().where('reference', reference).first();
      if (intent == null) {
        apiResponse = {
          success: false,
          message: "Aucun intention de paiement trouvé",
          result: null as any,
          except: null
        }
      } else {
        //check payment order status
        if (intent.status == 'paid' && intent.paidAt != null) {
          const payment = await Payment.query().where('payment_intent_id', Number(intent.id)).first();
          const wallet = await this.getWalletByUserId(intent.userId);
          apiResponse = {
            success: true,
            message: "Cette facture a été déjà réglée avec succès",
            result: {
              intent: intent,
              payment: payment,
              wallet: wallet,
            },
          }
        } else {
          const trait = new PaymentApiTrait();
          const checkPayment = await trait.getPaymentStatus(reference.toString());
          if (checkPayment.success) {
            const paymentResult = checkPayment.result as PaymentResponse;
            console.log("payment result,in check intent", paymentResult);

            if (paymentResult.state == 'Paid') {
              //update intent and create payment
              let intentMetadata = intent.metadata;
              let newMetadata = {
                state: paymentResult.state,
                code: intentMetadata.code,
                qrcode_url: intentMetadata.qrcode_url,
                bill_url: paymentResult.bill_url,
                payment_status: intentMetadata.payment_status,
                merchant_reference: paymentResult.merchant_reference,
                order_reference: paymentResult.order_reference,
                gateway_id: intentMetadata.gateway_id,
                payments_method: intentMetadata.payments_method
              }
              intent.metadata = JSON.stringify(newMetadata);
              intent.status = 'paid';
              intent.paidAt = DateTime.now();
              // intent.metadata = newMetadata;
              await intent.save();
              //create payment
              let metadata = {
                state: paymentResult.state,
                bill_url: paymentResult.bill_url,
                billed_amount: paymentResult.billed_amount,
                merchant_reference: paymentResult.merchant_reference,
                order_reference: paymentResult.order_reference,
                date_created: paymentResult.date_create,
                payment_method: intent.metadata.payments_method,
              }
              let trxRef = await this.generateUUID();
              let beneficiary_type = await this.getBeneficiaryTypeByUserId(intent.userId);
              const resPayment = await Payment.create({
                amount: intent.amountPaid,
                beneficiaryId: intent.beneficiaryId,
                beneficiaryType: beneficiary_type as "patient" | "laborantin" | "pharmacien" | "soignant" | null | undefined,
                paymentTypeId: intent.paymentTypeId,
                metadata: metadata,
                status: intent.status,
                paidAt: intent.paidAt,
                paymentGatewayId: intentMetadata.gateway_id ?? null,
                paymentIntentId: intent.id,
                trxRef: trxRef,
                description: "Le portefeuille a été rechargé avec un montant de " + intent.amountPaid + " " + intent.metadata.currency,
              }, { client: trx });
              if (resPayment !== null) {
                const wallet = await this.getWalletByUserId(intent.userId);
                if (wallet !== null) {
                  // let oldBalance = wallet.balance;
                  // let newBalance = oldBalance + intent.amountPaid;
                  // wallet.balance = newBalance;
                  // (await wallet.save()).useTransaction(trx);
                  let wtrait = new WalletTrait();
                  await wtrait.crediteWallet(wallet, intent.amountPaid, "deposit", intent.id, trx);
                  await trx.commit();
                  apiResponse = {
                    success: true,
                    message: "Le paiement a été effectué avec succès",
                    result: {
                      intent: intent,
                      payment: resPayment,
                      wallet: wallet
                    },
                  }
                }
              } else {
                await trx.rollback();
                apiResponse = {
                  success: false,
                  message: "Une erreur est survenue lors de la création du paiement,veuillez réessayer",
                  result: null as any,
                  except: intent
                }
              }
            } else {
              const failedStatus = ['Pending', 'Error'];
              let message = "";
              if (failedStatus.includes(paymentResult.state)) {
                message = paymentResult.state === 'Error' ? "Echec, votre paiement n'a pas abouti avec succès, veuillez réessayer" : "Votre paiement est en attente de validation";
              }
              await trx.rollback();
              apiResponse = {
                success: false,
                message: message,
                result: null as any,
                except: paymentResult
              }
            }
          } else {
            await trx.rollback();
            apiResponse = {
              success: false,
              message: "Echec, votre paiement n'a pas abouti avec succès, veuillez réessayer",
              result: null as any,
              except: intent
            }
          }
        }
      }
    } catch (error) {
      await trx.rollback();
      console.log("error in check wallet intent", error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
    }
    return response.status(200).json(apiResponse);
  }

  public async intentCallback({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    }
    const trx = await Database.transaction();
    try {
      console.log("callback request", request.input('token'));
      const payload = this.decodeToken(request.input('token'));
      console.log("payload", payload);
      if (payload.merchant_reference == null || payload.order_reference == null) {
        await trx.rollback();
        apiResponse = {
          success: false,
          message: "Aucune information de paiement trouvée",
          result: null as any,
          except: null
        }
      } else {
        const intent = await PaymentIntent.query().where('reference', payload.merchant_reference).first();
        if (intent) {
          let uuid = await this.generateUUID();
          const callback = await PaymentCallback.create({
            orderReference: payload.order_reference,
            merchantReference: payload.merchant_reference,
            state: payload.state,
            identifier: payload.identifier,
            billUrl: payload.bill_url,
            client: payload.client,
            receivedAmount: payload.received_amount,
            status: payload.status,
            message: payload.message,
            qrcodeUrl: payload.qrcode_url,
            payments: payload.payments,
            callbackUuid: uuid,
          }, { client: trx });
          if (callback) {
            await trx.commit();
            apiResponse = {
              success: true,
              message: "Callback received successfully",
              result: callback
            }
          } else {
            await trx.rollback();
            apiResponse = {
              success: false,
              message: "Une erreur est survenue lors de la création du callback",
              result: null as any,
              except: callback
            }
          }
        } else {
          await trx.rollback();
          apiResponse = {
            success: false,
            message: "Aucune intention de paiement trouvée",
            result: null as any,
            except: null
          }
        }
      }
    } catch (error) {
      await trx.rollback();
      console.log("error in intent callback", error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
    }
    return response.status(200).json(apiResponse);
  }

  private decodeToken(token: string) {
    const tokenParts = token.split(".");
    const tokenHeader = Buffer.from(tokenParts[0], 'base64').toString();

    const tokenPayload = Buffer.from(tokenParts[1], 'base64').toString();
    const result = JSON.parse(tokenPayload) as PaymentCallbackRequest;
    console.log("token header", tokenHeader);
    console.log("token payload", tokenPayload);

    return result;
  }

}
