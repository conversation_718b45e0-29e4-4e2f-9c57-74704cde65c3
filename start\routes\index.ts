import Route from '@ioc:Adonis/Core/Route';
import './apis/intent';
import './apis/payments';
import './apis/transacts';
import HelperController from 'App/Controllers/Http/api/helpers/HelperController';

Route.post('/test-auth', async ({ request, auth, response }) => {
  const user_agent = request.header('user-agent');
  const authuser = auth.authenticate();
  const bearer = request.header('Authorization');
  console.log("authorization", bearer);

  if (bearer) {
    const token = bearer.split(' ')[1];
    if (token) {
      console.log("token", token);

      const user = await auth.authenticate();
      if (!user) {
        return response.status(401).json({
          message: 'Token invalide ou expiré',
          user_agent,
          authuser
        });
      } else {
        console.log("Utilisateur authentifié avec succès", user);
        // Vous pouvez maintenant continuer avec l'authentification réussie
      }
    }
  }
});

Route.get('/test-hostname', async ({ request, response }) => {
  const user_agent = request.header('user-agent');
  const host = request.host();
  let protocol = request.protocol();
  let callback_url = protocol + "://" + host + "/api/payments/intent/callback";
  // console.log("hostName",host);
  // console.log("callback_url",callback_url);

  return response.status(200).json({
    user_agent,
    host,
    callback_url
  });
});

Route.get('/api/fix-quotation-request',async(ctx)=>{
  return new HelperController().fixQuotationRequestStatus(ctx);
});

Route.get('/api/fix-insurance-fees',async(ctx)=>{
  return new HelperController().fixInsuranceSubscriptionTotalAmountPaid(ctx);
});
