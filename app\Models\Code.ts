import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import Patient from './Patient'
import Card from './Card'

export default class Code extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column({ columnName: 'patient_id' })
  public patientId: number

  @column({ columnName: 'card_id' })
  public cardId: number

  @column()
  public validity: string

  @column({ columnName: 'blocked_at' })
  public blockedAt: string

  @column({ columnName: 'activated_at' })
  public activatedAt: string

  @column({ columnName: 'expired_at' })
  public expiredAt: string

  @column({columnName: 'online_paid'})
  public onlinePaid: number

  @belongsTo(() => Patient, {
    foreignKey: 'patientId',
    localKey: 'id'
  })
  public patient: BelongsTo<typeof Patient>

  @belongsTo(() => Card, {
    foreignKey: 'cardId',
    localKey: 'id'
  })
  public card: BelongsTo<typeof Card>
}
