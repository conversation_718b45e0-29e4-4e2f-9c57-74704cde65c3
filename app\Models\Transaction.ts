import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import User from './User'
import Patient from './Patient'
import Wallet from './Wallet'
import PaymentType from './PaymentType'

export default class Transaction extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'user_id' })
  public userId: number

  @column({ columnName: 'wallet_id' })
  public walletId: number

  @column()
  public amount: number

  @column({ columnName: 'payment_type_id' })
  public paymentTypeId: number

  @column()
  public description: string

  @column({ columnName: 'date_op' })
  public dateOp: Date

  @column()
  public status: string

  @column()
  public metadata: any

  @column({ columnName: 'trx_ref' })
  public trxRef: string

  @column()
  public balances: any

  @column({ columnName: 'paid_by_self' })
  public paidBySelf: boolean // Indique si le paiement a été effectué par l'utilisateur lui-même

  @column({ columnName: 'payer_id' })
  public payerId?: number // Identifiant de la personne ou entité ayant effectué le paiement pour le compte de l'utilisateur

  @column({ columnName: 'payer_wallet_id' })
  public payerWalletId?: number // Identifiant du wallet utilisé par le tiers pour effectuer le paiement

  @column({ columnName: 'beneficiary_id' })
  public beneficiaryId: number // Identifiant du compte patient bénéficiaire du paiement

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => User, {
    foreignKey: 'userId'
  })
  public user: BelongsTo<typeof User>

  @belongsTo(() => Patient, {
    foreignKey: 'beneficiaryId',
    localKey: 'id'
  })
  public beneficiary: BelongsTo<typeof Patient>

  @belongsTo(() => User, {
    foreignKey: 'payerId',
    localKey: 'id'
  })
  public payer: BelongsTo<typeof User>

  @belongsTo(() => Wallet, {
    foreignKey: 'walletId',
    localKey: 'id'
  })
  public wallet: BelongsTo<typeof Wallet>

  @belongsTo(() => Wallet, {
    foreignKey: 'payerWalletId',
    localKey: 'id'
  })
  public payerWallet: BelongsTo<typeof Wallet>

  @belongsTo(() => PaymentType, {
    foreignKey: 'paymentTypeId',
    localKey: 'id'
  })
  public paymentType: BelongsTo<typeof PaymentType>

}


