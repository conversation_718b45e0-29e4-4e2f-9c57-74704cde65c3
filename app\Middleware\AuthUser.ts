import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import HelperController from 'App/Controllers/Http/api/helpers/HelperController'

export default class AuthUser extends HelperController {
  public async handle({ request, response }: HttpContextContract, next: () => Promise<void>) {
    const token = request.header('dotoken');

    if (!token) {
      return response.status(401).json({
        success: false,
        message: "Vous n'êtes pas autorisé à accéder à cette ressource",
        result: null
      });
    }
    try {
      const decoded = await this.verifyJWT(token);

      // Vérifie que l'utilisateur est connecté
      if (!decoded || !decoded.user_id) {
        return response.status(401).json({
          success: false,
          message: "Vous n'êtes pas autorisé à accéder à cette ressource",
          result: null
        });
      }
      await next();
    } catch (error) {
      console.log("jwt error",error);

      return response.status(401).json({
        success: false,
        message: "Token invalide ou expiré",
        result: null
      });
    }
  }
}
