import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class Diagnostic extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column({ columnName: 'pro_id' })
  public proId: number

  @column({ columnName: 'fmd_id' })
  public fmdId: number

  @column({ columnName: 'patient_id' })
  public patientId: number

  @column({ columnName: 'health_institute_id' })
  public healthInstituteId: number

  @column({ columnName: 'reference' })
  public reference: string

  @column({ columnName: 'libelle' })
  public libelle: string

  @column({ columnName: 'content' })
  public content: string

  @column({ columnName: 'symptoms' })
  public symptoms: string[]

  @column({ columnName: 'treatments' })
  public treatments: string[]

  @column({ columnName: 'category_diagnostic_id' })
  public categoryDiagnosticId: number

  @column({ columnName: 'pathology_id' })
  public pathologyId: number

  @column({ columnName: 'protocol_traitement_id' })
  public protocolTraitementId: number

  @column({ columnName: 'cat' })
  public cat: string

  @column({ columnName: 'consultation_price' })
  public consultationPrice: number

  @column({ columnName: 'diagnostic_final' })
  public diagnosticFinal: any[]

  @column({ columnName: 'files' })
  public files: string[]
}
