import Route from '@ioc:Adonis/Core/Route';
import PaymentController from 'App/Controllers/Http/api/core/PaymentController';

const paymentCtrl = new PaymentController();
Route.group(() => {
  Route.group(() => {
    Route.get('/deposits', async (ctx) => {
      return paymentCtrl.getDeposits(ctx);
    });
    Route.get('/deposits/:payment_id/details', async (ctx) => {
      return paymentCtrl.getDepositDetails(ctx);
    });
    
  }).prefix('payments').middleware('auth-user');
}).prefix('api').namespace('App/Controllers/Http/api/core');
