import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import CategoryProduct from './CategoryProduct'
import ProductType from './ProductType'

export default class Product extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public name: string

  @column()
  public eng_name: string

  @column()
  public brand: string

  @column()
  public form: string

  @column()
  public code: string

  @column({columnName: 'category_product_id'})
  public productTypeId: number

  @column()
  public user_id: number

  @column()
  public description: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => ProductType, {
    foreignKey: 'productTypeId',
    localKey: 'id'
  })
  public productType: BelongsTo<typeof ProductType>
}
