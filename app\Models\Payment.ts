import { DateTime } from "luxon"
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import PaymentType from "./PaymentType"
import PaymentIntent from "./PaymentIntent"
import Patient from "./Patient"
import Soignant from "./Soignant"
import Pharmacien from "./Pharmacien"
import Laborantin from "./Laborantin"

export class Payment extends BaseModel {
  @column({ isPrimary: true, columnName: 'id' })
  public id: number

  @column({ columnName: 'payment_gateway_id' })
  public paymentGatewayId: number

  @column({ columnName: 'trx_ref' })
  public trxRef: string

  @column({ columnName: 'payment_type_id' })
  public paymentTypeId: number

  @column({ columnName: 'payment_intent_id' })
  public paymentIntentId: number

  @column({ columnName: 'beneficiary_id' })
  public beneficiaryId: number | null

  @column({ columnName: 'beneficiary_type' })
  public beneficiaryType: 'patient' | 'laborantin' | 'pharmacien' | 'soignant' | null

  @column({ columnName: 'amount' })
  public amount: number

  @column({ columnName: 'status' })
  public status: 'pending' | 'paid' | 'cancelled' | 'failed'

  @column({ columnName: 'description' })
  public description: string | null

  @column({ columnName: 'metadata' })
  public metadata: any

  @column.dateTime({ autoCreate: false, autoUpdate: false, columnName: 'paid_at' })
  public paidAt: DateTime | null

  @column.dateTime({ autoCreate: true, autoUpdate: true, columnName: 'created_at' })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true, columnName: 'updated_at' })
  public updatedAt: DateTime

  @belongsTo(() => PaymentType,{
    localKey: 'id',
    foreignKey: 'paymentTypeId'
  })
  public payment_type: BelongsTo<typeof PaymentType>

  @belongsTo(() => PaymentIntent,{ localKey: 'id', foreignKey: 'paymentIntentId' })
  public payment_intent: BelongsTo<typeof PaymentIntent>

  @belongsTo(() => Patient,{
    localKey: 'id',
    foreignKey: 'beneficiaryId',
  })
  public patient: BelongsTo<typeof Patient>

  @belongsTo(() => Laborantin,{
    localKey: 'id',
    foreignKey: 'beneficiaryId',
  })
  public laborantin: BelongsTo<typeof Laborantin>

  @belongsTo(() => Pharmacien,{
    localKey: 'id',
    foreignKey: 'beneficiaryId',
  })
  public pharmacien: BelongsTo<typeof Pharmacien>

  @belongsTo(() => Soignant,{
    localKey: 'id',
    foreignKey: 'beneficiaryId',
  })
  public soignant: BelongsTo<typeof Soignant>
}


