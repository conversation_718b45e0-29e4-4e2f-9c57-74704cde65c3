import { TransactionClientContract } from "@ioc:Adonis/Lucid/Database";
// import { formatErrorResponse } from "App/Controllers/Utils";
import { ApiResponse } from "App/Controllers/Utils/models";
import Wallet from "App/Models/Wallet";
import WalletHistory from "App/Models/WalletHistory";

export default class WalletTrait {

  public static async getWallet(userId: number): Promise<Wallet | null> {
    const wallet = await Wallet.query().where('user_id', userId).first();
    return wallet;
  }

  public async recordTransaction(wallet: Wallet, amount: number, type: string, description: string, trx: any, operationId: number): Promise<WalletHistory> {
    let transactionId = operationId;
    let paymentId = 0;
    if (type === "deposit") {
      paymentId = operationId;
    }
    return await WalletHistory.create({
      walletId: wallet.id,
      amount: amount,
      type: type as 'deposit' | 'withdrawal' | 'transfert' | 'payment',
      description: description,
      transactionId: transactionId !== 0 ? transactionId : null,
      paymentId: paymentId !== 0 ? paymentId : null,
      dateOp: new Date()
    }, { client: trx });
  }

  public async crediteWallet(wallet: Wallet, amount: number, type: string, operationId: number, trx: TransactionClientContract): Promise<ApiResponse> {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
      except: null
    };

    try {
      if (!wallet) {
        apiResponse.message = "Portefeuille introuvable";
      } else {
        const newBalance = Number(wallet.balance + amount);
        await wallet.useTransaction(trx).merge({ balance: newBalance }).save();

        const description = `Un crédit de ${amount} a été effectué sur votre portefeuille`;
        const history = await this.recordTransaction(wallet, amount, type, description, trx, operationId);

        if (history) {
          apiResponse = {
            success: true,
            message: "Crédit effectué avec succès",
            result: history,
          };
        } else {
          apiResponse.message = "Erreur lors de la création de l'opération de crédit";
          apiResponse.except = history;
        }
      }
    } catch (error) {

      apiResponse = {
        success: false,
        message: "Erreur lors du crédit du portefeuille",
        result: null,
        except: error.message
      };
    }

    return apiResponse;
  }


  public async debiteWallet(wallet: Wallet, amount: number, type: string, operationId: number, trx: TransactionClientContract,description?: string): Promise<ApiResponse> {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null,
      except: null
    };

    try {
      if (!wallet) {
        apiResponse.message = "Portefeuille introuvable";
      } else {
        const newBalance = Number(wallet.balance - amount);

        if (newBalance < 0) {
          apiResponse.message = "Solde insuffisant";
        } else {
          // Mettre à jour le solde avec la transaction active
          await wallet.useTransaction(trx).merge({ balance: newBalance }).save();
          // Enregistrer la transaction dans l'historique
          let descriptionText = description ? description : `Un retrait de ${amount} a été effectué sur votre portefeuille`;
          let history = await this.recordTransaction(wallet, amount, type, descriptionText, trx, operationId);

          if (history) {
            apiResponse = {
              success: true,
              message: "Retrait effectué avec succès",
              result: history,
            };
          } else {
            // throw new Error("Erreur lors de la création de l'opération de retrait");
            apiResponse.message = "Erreur lors de la création de l'opération de retrait";
            apiResponse.except = history;

          }
        }
      }
    } catch (error) {
      apiResponse = {
        success: false,
        message: "Erreur lors du débit du portefeuille",
        result: null,
        except: error.message
      };
    }

    return apiResponse;
  }


  public async transferWallet(wallet_sender: Wallet, wallet_receiver: Wallet, amount: number, type: string, operationId: number, trx: TransactionClientContract): Promise<ApiResponse> {
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    };

    try {
      if (!wallet_sender || !wallet_receiver) {
        return {
          success: false,
          message: "Portefeuille introuvable",
          result: null
        };
      }
      const newSenderBalance = Number(wallet_sender.balance - amount);
      if (newSenderBalance < 0) {
        return {
          success: false,
          message: "Solde insuffisant",
          result: null
        };
      }

      // Mettre à jour le solde des portefeuilles avec la transaction active
      const newReceiverBalance = Number(wallet_receiver.balance + amount);

      await wallet_sender.useTransaction(trx).merge({ balance: newSenderBalance }).save();
      await wallet_receiver.useTransaction(trx).merge({ balance: newReceiverBalance }).save();


      // Enregistrer la transaction dans l'historique
      const senderDescription = `Un retrait de ${amount} a été effectué sur votre portefeuille`;
      const receiverDescription = `Un dépôt de ${amount} a été effectué sur votre portefeuille`;

      const senderHistory = await this.recordTransaction(wallet_sender, amount, type, senderDescription, trx, operationId);
      const receiverHistory = await this.recordTransaction(wallet_receiver, amount, type, receiverDescription, trx, operationId);

      // Vérifier si l'enregistrement de l'historique a réussi
      if (senderHistory && receiverHistory) {
        apiResponse = {
          success: true,
          message: "Transfert effectué avec succès",
          result: { senderHistory, receiverHistory }
        };
      } else {
        apiResponse = {
          success: false,
          message: "Erreur lors de la création de l'opération de transfert",
          result: null
        };
      }

    } catch (error) {
      console.log("Erreur dans transferWallet:", error);
      apiResponse = {
        success: false,
        message: "Erreur lors du transfert de portefeuille",
        result: null,
        except: error.message
      };
    }

    return apiResponse;
  }

}
