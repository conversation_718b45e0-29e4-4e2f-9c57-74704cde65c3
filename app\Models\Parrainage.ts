import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class Parrainage extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'parrain_id' })
  public parrainId: number

  @column({ columnName: 'user_parrained_id' })
  public userParrainedId: number

  @column({ columnName: 'code_parrainage' })
  public codeParrainage: string | null

  @column()
  public type: 'DO' | 'QRCODE' | 'ADHESION'

  @column({ columnName: 'last_name' })
  public lastName: string | null

  @column({ columnName: 'first_name' })
  public firstName: string | null

  @column()
  public phone: string | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
