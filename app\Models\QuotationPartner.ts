import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import QuotationRequest from './QuotationRequest'
import Pharmacy from './Pharmacy'
import Laboratory from './Laboratory'

export default class QuotationPartner extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'quotation_request_id' })
  public quotationRequestId: number // La demande liée

  @column({ columnName: 'pharmacy_id' })
  public pharmacyId?: number // Pharmacie

  @column({ columnName: 'laboratory_id' })
  public laboratoryId?: number // Laboratoire

  @column({ columnName: 'is_notified' })
  public isNotified: boolean = false

  @column({ columnName: 'status' })
  public status: 'pending' | 'accepted' | 'rejected' | 'expired' | 'completed' | 'partially_end' = 'pending'

  @column({ columnName: 'notified_at' })
  public notifiedAt?: string

  @column.dateTime({ columnName: 'accepted_at' })
  public acceptedAt?: DateTime

  @column.dateTime({ columnName: 'rejected_at' })
  public rejectedAt?: DateTime

  @column.dateTime({ columnName: 'expired_at' })
  public expiredAt?: DateTime

  @column.dateTime({ columnName: 'completed_at' })
  public completedAt?: DateTime

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => QuotationRequest)
  public quotation_request: BelongsTo<typeof QuotationRequest>

  @belongsTo(() => Pharmacy)
  public pharmacy: BelongsTo<typeof Pharmacy>

  @belongsTo(() => Laboratory)
  public laboratory: BelongsTo<typeof Laboratory>

}
