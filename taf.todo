
public async payQrcodeForBeneficiary({ request, response }: HttpContextContract) {
  let apiResponse: ApiResponse = {
    success: false,
    message: '',
    result: null as any,
    except: null as any,
  };

  const token = request.header('dotoken');

  if (!token) {
    apiResponse.message = "Token d'authentification manquant";
    return response.status(401).json(apiResponse);
  }

  try {
    // Valider et extraire les données du payload
    const payload = await request.validate({
      schema: schema.create({
        beneficiary_id: schema.number(),
        code_parrainage: schema.string.optional(),
      }),
    });

    const { beneficiary_id, code_parrainage } = payload;

    // Vérifier l'authenticité du token et récupérer l'utilisateur
    const userPayload = await this.verifyJWT(String(token));
    if (!userPayload || !userPayload.user_id) {
      apiResponse.message = "Token invalide ou expiré";
      return response.status(401).json(apiResponse);
    }

    const userId = userPayload.user_id;

    // Vérifier si l'utilisateur est un parent assuré
    const parent = await this.getPatientByUserId(userId);
    if (!parent) {
      apiResponse.message = "Utilisateur non trouvé ou n'est pas un parent assuré";
      return response.status(404).json(apiResponse);
    }

    // Vérifier l'existence du bénéficiaire et son statut
    const beneficiaryValidation = await this.validateBeneficiary(beneficiary_id, parent.id);
    if (!beneficiaryValidation.success) {
      apiResponse.message = beneficiaryValidation.message;
      return response.status(beneficiaryValidation.statusCode).json(apiResponse);
    }

    const beneficiary = beneficiaryValidation.beneficiary;
    const patient = beneficiaryValidation.patient;

    // Récupérer le portefeuille du parent et vérifier le solde
    const walletValidation = await this.validateWalletAndBalance(parent.user_id, 'QRCODE');
    if (!walletValidation.success) {
      apiResponse.message = walletValidation.message;
      return response.status(walletValidation.statusCode).json(apiResponse);
    }

    const wallet = walletValidation.wallet;
    const qrcodeFees = walletValidation.fees;

    // Sélectionner une carte disponible
    const card = await Card.query()
      .where('is_paid', false)
      .andWhere('is_used', false)
      .andWhere('status', 'new')
      .andWhere('type', 'online') // Ou 'physical' selon vos besoins
      .forUpdate()
      .first();

    if (!card) {
      apiResponse.message = "Aucune carte disponible";
      return response.status(400).json(apiResponse);
    }

    // Démarrer une transaction
    const trx = await Database.transaction();

    try {
      // Créer la transaction de paiement
      const paymentResult = await this.createPaymentTransaction(
        parent,
        patient,
        wallet,
        card,
        qrcodeFees,
        trx
      );

      if (!paymentResult.success) {
        await trx.rollback();
        apiResponse.message = paymentResult.message;
        return response.status(500).json(apiResponse);
      }

      const { transact, newCode } = paymentResult;

      // Gestion du parrainage (optionnel)
      if (code_parrainage) {
        const parrainage = await this.addQrcodeParrainage(
          trx,
          code_parrainage,
          parent.user_id,
          patient.last_name,
          patient.first_name
        );

        if (!parrainage.success) {
          await trx.rollback();
          apiResponse.message = parrainage.message;
          return response.status(500).json(apiResponse);
        }
      }

      // Valider la transaction
      await trx.commit();

      // Réponse réussie
      apiResponse.success = true;
      apiResponse.message = "Paiement de la carte effectué avec succès pour le bénéficiaire.";
      apiResponse.result = {
        beneficiary,
        patient,
        card,
        code: newCode,
        wallet,
        transact,
      };

      return response.status(200).json(apiResponse);
    } catch (error) {
      await trx.rollback();
      console.error("Error in payQrcodeForBeneficiary:", error.message);
      apiResponse.message = formatErrorResponse(error);
      apiResponse.except = error.message;
      return response.status(500).json(apiResponse);
    }
  } catch (error) {
    console.error("Error in payQrcodeForBeneficiary:", error.message);
    apiResponse.message = formatErrorResponse(error);
    apiResponse.except = error.message;
    return response.status(500).json(apiResponse);
  }
}

// Fonction pour valider le bénéficiaire
private async validateBeneficiary(beneficiary_id: number, parentId: number): Promise<{ success: boolean; message: string; statusCode: number; beneficiary?: any; patient?: any }> {
  const beneficiary = await InsuranceCompanyBeneficiary.query()
    .where('id', beneficiary_id)
    .where('parent_id', parentId)
    .first();

  if (!beneficiary) {
    return { success: false, message: "Bénéficiaire introuvable", statusCode: 404 };
  }

  const patient = await this.getPatientByBeneficiaryId(beneficiary_id);

  if (!patient || patient.carnet_is_active) {
    return {
      success: false,
      message: patient ? "Le carnet est déjà activé" : "Patient non trouvé",
      statusCode: 400,
    };
  }

  return { success: true, message: "", statusCode: 200, beneficiary, patient };
}

// Fonction pour valider le wallet et le solde
private async validateWalletAndBalance(userId: number, paymentTypeName: string): Promise<{ success: boolean; message: string; statusCode: number; wallet?: any; fees?: number }> {
  const wallet = await this.getWalletByUserId(userId);

  if (!wallet) {
    return { success: false, message: "Portefeuille non trouvé", statusCode: 404 };
  }

  const paymentType = await this.getPaymentTypeByName(paymentTypeName);

  if (!paymentType || !paymentType.configs?.amount) {
    return { success: false, message: "Type de paiement introuvable", statusCode: 400 };
  }

  const fees = Number(paymentType.configs.amount);

  if (wallet.balance < fees) {
    return { success: false, message: "Solde insuffisant", statusCode: 400 };
  }

  return { success: true, message: "", statusCode: 200, wallet, fees };
}

// Fonction pour créer la transaction de paiement
private async createPaymentTransaction(
  parent: any,
  patient: any,
  wallet: any,
  card: any,
  qrcodeFees: number,
  trx: any
): Promise<{ success: boolean; message: string; transact?: any; newCode?: any }> {
  const trxRef = await this.generateUUID();
  const description = "Paiement de la carte QRCODE pour un bénéficiaire";

  const metadata = {
    cardId: card.id,
    paymentType: "QRCODE",
    amount: qrcodeFees,
    patient: `${patient.last_name} ${patient.first_name}`,
    beneficiaryId: patient.id,
  };

  // Créer la transaction
  const transact = await Transaction.create(
    {
      userId: parent.user_id,
      beneficiaryId: patient.id,
      paidBySelf: false,
      walletId: wallet.id,
      amount: qrcodeFees,
      description: description,
      status: "pending",
      paymentTypeId: (await this.getPaymentTypeByName("QRCODE")).id,
      dateOp: new Date(),
      trxRef: trxRef,
      metadata: metadata,
      balances: {
        last_balance: wallet.balance,
        new_balance: wallet.balance - qrcodeFees,
      },
    },
    { client: trx }
  );

  if (!transact) {
    return { success: false, message: "Erreur lors de la création de la transaction" };
  }

  // Débiter le wallet
  const trait = new WalletTrait();
  const debite = await trait.debiteWallet(wallet, qrcodeFees, "payment", transact.id, trx);

  if (!debite.success) {
    return { success: false, message: debite.message };
  }

  // Créer le code associé à la carte
  const validity = 365;
  const newCode = await Code.create(
    {
      patientId: patient.id,
      cardId: card.id,
      validity: validity.toString(),
      activatedAt: DateTime.now().toISODate(),
      expiredAt: DateTime.now().plus({ days: validity }).toISODate(),
      onlinePaid: 1,
    },
    { client: trx }
  );

  if (!newCode) {
    return { success: false, message: "Erreur lors de la création du code" };
  }

  // Mettre à jour la carte et le patient
  await card.merge({ isUsed: true, isPaid: true, status: 'used' }).useTransaction(trx).save();
  await patient.merge({ carnet_is_active: true }).useTransaction(trx).save();

  // Mettre à jour la transaction comme payée
  transact.status = "paid";
  await transact.useTransaction(trx).save();

  return { success: true, message: "", transact, newCode };
}

// Fonction pour valider la carte
  private async validateCard(card_uuid: string, trx: any): Promise<{ success: boolean; message: string; card?: Card }> {
    const card = await Card.query()
      .useTransaction(trx)
      .where('type', 'physical')
      .where('uid', card_uuid)
      .where('is_used', false)
      .andWhere('status', 'new')
      .forUpdate()
      .first();

    if (!card) {
      return { success: false, message: "Cette carte n'existe pas ou est déjà utilisée." };
    }

    if (card.status === 'blocked') {
      return { success: false, message: "Carnet bloqué." };
    }

    if (card.status === 'used') {
      return { success: false, message: "Carnet déjà en cours d'utilisation." };
    }

    return { success: true, message: "", card };
  }