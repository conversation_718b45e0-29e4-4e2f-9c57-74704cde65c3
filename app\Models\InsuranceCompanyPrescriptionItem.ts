import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, BelongsTo } from '@ioc:Adonis/Lucid/Orm'
import Patient from './Patient'
import PatientInsuranceCompany from './PatientInsuranceCompany'
import InsuranceCompanyPrescription from './InsuranceCompanyPrescription'
import PrescriptionItem from './PrescriptionItem'
import QuotationProposal from './QuotationProposal'

export default class InsuranceCompanyPrescriptionItem extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'patient_id' })
  public patientId: number

  @column({ columnName: 'patient_company_assurance_id' })
  public patientCompanyAssuranceId: number

  @column({ columnName: 'insurance_company_prescription_id' })
  public insuranceCompanyPrescriptionId: number

  @column({ columnName: 'prescription_item_id' })
  public prescriptionItemId: number

  @column({ columnName: 'quotation_proposal_id' })
  public quotationProposalId: number | null

  @column({ columnName: 'is_active' })
  public isActive: boolean

  @column({ columnName: 'total_quantity_assured' })
  public totalQuantityAssured: number

  @column({ columnName: 'total_amount_assured' })
  public totalAmountAssured: number

  @column({ columnName: 'status' })
  public status: 'pending' | 'validated' | 'blocked' | 'rejected'

  @column.dateTime({ autoCreate: true, columnName: 'created_at' })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true, columnName: 'updated_at' })
  public updatedAt: DateTime

  // Relations
  @belongsTo(() => Patient, {
    foreignKey: 'patientId',
  })
  public patient: BelongsTo<typeof Patient>

  @belongsTo(() => PatientInsuranceCompany, {
    foreignKey: 'patientCompanyAssuranceId',
  })
  public patientCompanyAssurance: BelongsTo<typeof PatientInsuranceCompany>

  @belongsTo(() => InsuranceCompanyPrescription, {
    foreignKey: 'insuranceCompanyPrescriptionId',
  })
  public insuranceCompanyPrescription: BelongsTo<typeof InsuranceCompanyPrescription>

  @belongsTo(() => PrescriptionItem, {
    foreignKey: 'prescriptionItemId',
  })
  public prescriptionItem: BelongsTo<typeof PrescriptionItem>

  @belongsTo(() => QuotationProposal, {
    foreignKey: 'quotationProposalId',
  })
  public quotationProposal: BelongsTo<typeof QuotationProposal>
}
