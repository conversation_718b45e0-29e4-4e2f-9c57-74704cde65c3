import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class PaymentCallback extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'order_reference' })
  public orderReference: string

  @column({ columnName: 'merchant_reference' })
  public merchantReference: string

  @column({ columnName: 'state' })
  public state: 'Pending'|'Error' | 'Paid' |'Partial' | 'Excess'

  @column({ columnName: 'identifier' })
  public identifier: string

  @column({ columnName: 'bill_url' })
  public billUrl: string

  @column({ columnName: 'client' })
  public client: any

  @column({ columnName: 'received_amount' })
  public receivedAmount: number

  @column({ columnName: 'status' })
  public status: string

  @column({ columnName: 'message' })
  public message: string

  @column({ columnName: 'qrcode_url' })
  public qrcodeUrl: string

  @column({ columnName: 'payments' })
  public payments: any

  @column({ columnName: 'callback_uuid' })
  public callbackUuid: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
