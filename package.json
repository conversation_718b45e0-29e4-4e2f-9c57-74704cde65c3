{"name": "payment-service", "version": "1.0.0", "private": true, "scripts": {"dev": "node ace serve --watch", "build": "node ace build --production", "start": "node server.js", "test": "node ace test"}, "devDependencies": {"@adonisjs/assembler": "^5.9.6", "@japa/preset-adonis": "^1.2.0", "@japa/runner": "^2.5.1", "@types/jsonwebtoken": "^9.0.7", "@types/proxy-addr": "^2.0.3", "@types/source-map-support": "^0.5.10", "adonis-preset-ts": "^2.1.0", "pino-pretty": "^11.2.2", "typescript": "~4.6", "youch": "^3.3.3", "youch-terminal": "^2.2.3"}, "dependencies": {"@adonisjs/auth": "^8.2.3", "@adonisjs/core": "^5.9.0", "@adonisjs/lucid": "^18.4.0", "@adonisjs/redis": "^7.3.4", "@adonisjs/repl": "^3.1.11", "crypto-js": "^4.2.0", "jsonwebtoken": "^9.0.2", "luxon": "^3.5.0", "mysql2": "^3.11.0", "nats": "^2.29.3", "payment-service": "file:", "proxy-addr": "^2.0.7", "reflect-metadata": "^0.2.2", "source-map-support": "^0.5.21"}}