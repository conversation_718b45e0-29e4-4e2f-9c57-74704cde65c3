import { DateTime } from 'luxon'
import { BaseModel, BelongsTo, belongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import TypeHealthInstitute from './TypeHealthInstitute'
import Country from './Country'
import City from './City'
import Quarter from './Quarter'

export default class HealthInstitute extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column({ columnName: 'name' })
  public name: string

  @column({ columnName: 'type_health_institute_id' })
  public typeHealthInstituteId: number

  @column({ columnName: 'phone' })
  public phone: string

  @column({ columnName: 'email' })
  public email: string

  @column({ columnName: 'website' })
  public website: string

  @column({ columnName: 'country_id' })
  public countryId: number

  @column({ columnName: 'city_id' })
  public cityId: number

  @column({ columnName: 'quarter_id' })
  public quarterId: number

  @column({ columnName: 'prefecture' })
  public prefecture: string

  @column({ columnName: 'address' })
  public address: string

  @column({ columnName: 'location' })
  public location: string

  @column({ columnName: 'description' })
  public description: string

  @column({ columnName: 'schedules' })
  public schedules: string

  @column({ columnName: 'responsable' })
  public responsable: string

  @column({ columnName: 'personals' })
  public personals: string

  @column({ columnName: 'status' })
  public status: string

  @belongsTo(() => TypeHealthInstitute, { foreignKey: 'type_health_institute_id' })
  public typeHealthInstitute: BelongsTo<typeof TypeHealthInstitute>

  @belongsTo(() => Country, {
    foreignKey: 'country_id',
    localKey: 'id'
  })
  public country: BelongsTo<typeof Country>

  @belongsTo(() => City, {
    foreignKey: 'city_id',
    localKey: 'id'
  })
  public city: BelongsTo<typeof City>

  @belongsTo(() => Quarter, {
    foreignKey: 'quarter_id',
    localKey: 'id'
  })
  public quarter: BelongsTo<typeof Quarter>

}
