import { DateTime } from 'luxon'
import { BaseModel, afterFetch, column } from '@ioc:Adonis/Lucid/Orm';

type Method = {
  id: number;
  libelle: string;
  methode: string;
  logo_url?: string;
  fees: number;
}

type Credential = {
  login: string;
  password: string;
  username?: string;
  client_id?: string;
  client_secret?: string;
  api_reference: string;
}

export default class PaymentGateway extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public name: string

  @column()
  public description?: string

  @column()
  public apiKey: string

  @column({columnName: 'credential'})
  public credential?: Credential

  @column()
  public apiUrl?: string

  @column()
  public methods?: Method[];

  @column()
  public configs?: any

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @afterFetch()
  public static setMethods(gateway: PaymentGateway) {
    if (typeof gateway.methods === 'string') {
      gateway.methods = JSON.parse(gateway.methods);
    }
  }
}

