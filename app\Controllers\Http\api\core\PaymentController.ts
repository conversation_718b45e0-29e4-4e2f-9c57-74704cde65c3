import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

// import { schema } from '@ioc:Adonis/Core/Validator';
import { formatErrorResponse } from "App/Controllers/Utils";
import { ApiResponse } from "App/Controllers/Utils/models";
import HelperController from '../helpers/HelperController';
import { Payment } from 'App/Models/Payment';
// import Database from '@ioc:Adonis/Lucid/Database';
// import WalletTrait from "App/Controllers/Http/api/traits/WalletTrait";
// import Transaction from 'App/Models/Transaction';
// import Card from 'App/Models/Card';
// import Patient from 'App/Models/Patient';
// import { DateTime } from 'luxon';
// import Code from 'App/Models/Code';

export default class PaymentController extends HelperController {

  public async getDeposits({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    }
    let token = request.header('dotoken');
    try {
      const page = request.input('page') || 1;
      const limit = request.input('limit') || 50;
      const userId = (await this.verifyJWT(String(token))).user_id as number;
      const user = await this.getUserById(userId);
      if (user !== null) {
        const entity = await this.getEntityByUserId(userId, Number(user.roleId));
        if (entity === null) {
          apiResponse = {
            success: false,
            message: "Aucun utilisateur trouvé",
            result: null as any,
            except: null
          }
          return response.status(200).json(apiResponse);
        }
        // console.log("beneficiary_id",entity);

        const payments = await Payment.query().where('beneficiary_id', entity?.id).orderBy('created_at', 'desc')
          .preload('payment_type').preload('patient')
          .preload('payment_intent')
          .paginate(page, limit);

        apiResponse = {
          success: true,
          message: "Payments received successfully",
          result: payments,
        }
      }
    } catch (error) {
      console.log("error in get payments", error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
    }
    return response.status(200).json(apiResponse);
  }


  public async getDepositDetails({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    }
    try {
      const paymentId = request.input('payment_id');
      const userId = request.input('user_id');
      const user = await this.getUserById(userId);
      if (user !== null) {
        const entity = await this.getEntityByUserId(userId, Number(user.roleId));
        if (entity === null) {
          apiResponse = {
            success: false,
            message: "Aucun utilisateur trouvé",
            result: null as any,
            except: null
          }
          return response.status(200).json(apiResponse);
        }
        const payments = await Payment.query().where('id', paymentId).where('beneficiaryId', entity?.id).orderBy('created_at', 'desc')
          .preload('payment_type')
          .preload('payment_intent')
          .preload('patient')
          .preload('laborantin')
          .preload('pharmacien')
          .preload('soignant')
          .first();

        apiResponse = {
          success: true,
          message: "Payment details retrieved successfully",
          result: payments,
        }
      } else {
        apiResponse = {
          success: false,
          message: "Aucun utilisateur trouvé",
          result: null as any,
          except: null
        }
      }
    } catch (error) {
      console.log("error in get payments", error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
    }
    return response.status(200).json(apiResponse);
  }








}
