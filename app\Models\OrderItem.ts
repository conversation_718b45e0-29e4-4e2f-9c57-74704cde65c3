import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class OrderItem extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column()
  public orderId: number 

  @column({ columnName: 'prescription_item_id' })
  public prescriptionItemId: number | null

  @column({ columnName: 'analyze_ask_item_id' })
  public analyzeAskItemId: number | null

  @column({ columnName: 'quotation_proposal_item_id' })
  public quotationProposalItemId: number | null

  @column()
  public quantity: number

  @column({ columnName: 'total_price' })
  public totalPrice: number | null

  @column({ columnName: 'total_assured_price' })
  public totalAssuredPrice: number | null

  @column({ columnName: 'paid_at' })
  public paidAt: string | null

  @column({ columnName: 'shipped_at' })
  public shippedAt: string | null

  @column({ columnName: 'cancelled_at' })
  public cancelledAt: string | null

  @column()
  public status: 'paid' | 'partially_paid' | 'shipped' | 'cancelled' | 'pending'
}
