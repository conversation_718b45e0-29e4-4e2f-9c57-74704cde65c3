import { formatErrorResponse } from "App/Controllers/Utils";
import { ApiResponse, PaymentOrder, PaymentResponse } from "App/Controllers/Utils/models";
import PaymentGateway from "App/Models/PaymentGateway";
import sha256 from "crypto-js/sha256";

type PaymentHeader = {
  login: string;
  apireference: string;
  salt: number;
  apisecure: string;
}
export default class PaymentApiTrait {

  public async initGateway(){
    let data = {
      headers: {} as PaymentHeader,
      apiURL: ""
    }
    try {
      const gateway = await PaymentGateway.first();
      if (gateway) {
        const apiKey = gateway.apiKey;
        const credential = gateway.credential;
        const apiURL = String(gateway.apiUrl);

        let login = String(credential?.login);
        let api_reference = Number(credential?.api_reference);
        let salt = new Date().getTime();
        let some = login + apiKey + salt;
        let hash = sha256(some).toString();

        let headers: PaymentHeader = {
          login: login,
          apireference: api_reference.toString(),
          salt: salt,
          apisecure: hash,
        }
        data = {
          headers: headers,
          apiURL: apiURL
        }
      }
    } catch (error) {
      console.log("error in get payment gateway",error);
    }
    return data
  }

  public async getAuthToken(): Promise<ApiResponse> {
    let data = {
      token: null,
      apiURL: ""
    }
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    try {
      const gateway = await PaymentGateway.first();
      if (gateway) {
        // const apiKey = gateway.apiKey;
        const credential = gateway.credential;
        const apiURL = String(gateway.apiUrl);
        data.apiURL = apiURL;

        let payload = {
          username: credential?.username,
          password: credential?.password,
          client_id: credential?.client_id,
          client_secret: credential?.client_secret,
          api_reference: credential?.api_reference
        }

        const response = await fetch(apiURL+'auth', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(payload),
        });

        if (response.ok) {
          console.log("AUTH TOKEN SENT");
          const result = await response.json() as any;
          console.log("api result",result);
          console.log("response status",response.status);

          if (result.access_token) {
            data.token = result.access_token;
            apiResponse = {
              success: true,
              message: "Token created successfully",
              result: data,
              except: null as any
            }
          }else{
            apiResponse = {
              success: false,
              message: result,
              result: null,
              except: result
            }
          }

        } else {
          const error = await response.json() as any;
          console.log("error in get auth token",error);
          apiResponse = {
            success: false,
            message: "Error",
            result: null as any,
            except: error
          }
        }
      }
    } catch (error) {
      console.log("error in get payment gateway",error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
    }
    return apiResponse;
  }

  public async createOrder(order: PaymentOrder){
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    try {
      // const resAuth = await this.getAuthToken();
      // console.log("api gateway",resAuth);
      console.log("order",order);

      const gateway = await this.initGateway();

      if (gateway) {
        const heards = gateway.headers;
        let newHeaders = {
          ...heards,
          'Content-Type': 'application/json'
        } as any;

        const response = await fetch(gateway.apiURL+'orders', {
          method: 'POST',
          headers: newHeaders,
          body: JSON.stringify(order),
        });

        if (response.ok) {
          console.log("ORDER PAYMENT SENT");

          const result = await response.json() as any;
          console.log("payment response",result);

          if (result.status == 'success') {
            apiResponse = {
              success: true,
              message: "Payment order created successfully",
              result: result,
              except: null as any
            }
          }else{
            apiResponse = {
              success: false,
              message: result.message,
              result: null as any,
              except: result
            }
          }
        } else {
          let error = await response.json() as any;
          console.log("error in create order", error);
          apiResponse = {
            success: false,
            message: "Error",
            result: null as any,
            except: error
          }
        }
      }else{
        apiResponse = {
          success: false,
          message: "Aucun gateway trouvé",
          result: null as any,
        }
      }
    } catch (error) {
      console.log("error in get payment gateway",error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
    }
    return apiResponse
  }

  public async getPaymentStatus(orderId: string){
    let apiResponse: ApiResponse = {
      success: false,
      message: "",
      result: null
    }
    try {
      const gateway = await this.initGateway();
      if (gateway && gateway.headers) {
        const heards = gateway.headers;
        let newHeaders = {
          ...heards,
          'Content-Type': 'application/json'
        } as any;
        const response = await fetch(gateway.apiURL+'orders/'+orderId+'/status', {
          method: 'GET',
          headers: newHeaders,
        });

        if (response.ok) {
          // console.log("ORDER PAYMENT STATTUS SENT");

          const result = await response.json() as PaymentResponse ;
          console.log("payment result",result);

          apiResponse = {
            success: true,
            message: "Payment order check successfully",
            result: result,
          }
        } else {
          const error = await response.json() as any;
          apiResponse = {
            success: false,
            message: "Error",
            result: null as any,
            except: error
          }
        }
      }
    } catch (error) {
      console.log("error in get payment gateway",error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
    }
    return apiResponse;
  }
}
