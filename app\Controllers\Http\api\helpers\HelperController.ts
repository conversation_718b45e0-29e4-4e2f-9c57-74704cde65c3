import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

import Database, { TransactionClientContract } from "@ioc:Adonis/Lucid/Database";
import { ApiResponse, CardStock, MonetizationBalance, PaymentTranche, UserParrainage } from 'App/Controllers/Utils/models';
import AnalyzeAskItem from "App/Models/AnalyzeAskItem";
import AppointmentType from 'App/Models/AppointmentType';
import InsuranceCompanySubscription from 'App/Models/InsuranceCompanySubscription';
import Laborantin from "App/Models/Laborantin";
import Monetization from 'App/Models/Monetization';
import OrderItem from "App/Models/OrderItem";
import Parrainage from 'App/Models/Parrainage';
import ParrainageSetting from 'App/Models/ParrainageSetting';
import Patient from "App/Models/Patient";
import PaymentType from "App/Models/PaymentType";
import Pharmacien from "App/Models/Pharmacien";
import PrescriptionItem from "App/Models/PrescriptionItem";
import QuotationRequest from "App/Models/QuotationRequest";
import Service from 'App/Models/Service';
import Soignant from "App/Models/Soignant";

import User from "App/Models/User";
import UserConnection from "App/Models/UserConnection";
import Wallet from "App/Models/Wallet";
import jwt from 'jsonwebtoken';
import { DateTime } from 'luxon';
import Card from 'App/Models/Card';
import CardOrder from 'App/Models/CardOrder';
import InsuranceCompanyFee from 'App/Models/InsuranceCompanyFee';
import InsuranceYear from 'App/Models/InsuranceYear';
export default class HelperController {


  public async verifyJWT(token: string) {
    const secret = process.env.JWT_SECRET;
    if (!secret || secret === '') {
      throw new Error('JWT Secret is missing');
    }
    try {
      // Vérifie et décode le token avec la clé secrète
      const decoded = jwt.verify(token, secret, {
        issuer: 'dokitaEyes'
      }) as jwt.JwtPayload;
      const { user_id, role } = decoded;
      return { user_id, role };

    } catch (err) {
      console.log("jwt token", err);
      if (err instanceof jwt.TokenExpiredError) {
        throw new Error('Token has expired');
      } else if (err instanceof jwt.JsonWebTokenError) {
        throw new Error('Invalid token');
      } else {
        throw new Error('Token verification failed');
      }
    }
  }
  /**
   * checkUserAuth
   * @param userId
   * @returns {Promise<boolean>}
   */
  public async checkUserAuth(userId: number): Promise<boolean> {
    let isAuth = false;
    if (userId) {
      const user = await User.query().where('id', userId).first();
      if (user !== null && user.online === 1) {
        const lastLogin = await Database.query().from('api_tokens').where('user_id', userId).orderBy('created_at', 'desc').first();
        if (lastLogin !== null) {
          const lastActivity = await UserConnection.query().where('user_id', userId).where('type', 'login').where('connected_at', lastLogin.created_at).orderBy('created_at', 'desc').first();
          if (lastActivity !== null) {
            isAuth = true;
          }
        }
      }
    }
    return isAuth;
  }

  public async getUserById(userId: number): Promise<User | null> {
    const res = User.query().where('id', userId).preload('country').first();
    if (res == null) {
      return null;
    }
    return res;
  }

  public async getWalletByUserId(userId: number): Promise<Wallet | null> {
    const res = Wallet.query().where('user_id', userId).forUpdate().firstOrFail();
    if (res == null) {
      return null;
    }
    return res;
  }

  public async getPatientByUserId(userId: number): Promise<Patient | null> {
    const res = Patient.query().where('user_id', userId).first();
    if (res == null) {
      return null;
    }
    return res;
  }

  public async generateWalletCode() {
    const gens = "**********";
    let length = 12;
    let code = '';
    for (let i = 0; i < length; i++) {
      code += gens.charAt(Math.floor(Math.random() * gens.length));
    }
    return code;
  }

  public async getEntityByUserId(userId: number, roleId: number): Promise<Patient | Soignant | Pharmacien | Laborantin | null> {
    let entity = {} as Patient | Soignant | Pharmacien | Laborantin;
    switch (roleId) {
      case 3:
        entity = await Soignant.query().where('user_id', userId).first() as Soignant;
        break;
      case 4:
        entity = await Pharmacien.query().where('user_id', userId).first() as Pharmacien;
        break;
      case 5:
        entity = await Laborantin.query().where('user_id', userId).first() as Laborantin;
        break;
      case 6:
        entity = await Patient.query().where('user_id', userId).first() as Patient;
        break;
      default:
        entity = await Patient.query().where('user_id', userId).first() as Patient;
        break;
    }
    return entity;
  }

  public async generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
      const r = (Math.random() * 16) | 0;
      const v = c === 'x' ? r : (r & 0x3) | 0x8;
      return v.toString(32);
    });
  }

  public async getBeneficiaryTypeByUserId(userId: number): Promise<string | null> {
    const user = await User.query().where('id', userId).first();
    if (user !== null) {
      if (user.roleId === 3) {
        return 'soignant';
      } else if (user.roleId === 4) {
        return 'pharmacien';
      } else if (user.roleId === 5) {
        return 'laborantin';
      } else if (user.roleId === 6) {
        return 'patient';
      }
    }
    return null;
  }

  public async getPaymentTypeByName(name: string): Promise<PaymentType | null> {
    const paymentType = await PaymentType.query().where('name', name).first();
    return paymentType;
  }

  public async isTotalPaidForItems(items: any[], orderItemsToPay: any[]): Promise<boolean> {
    if (items.length > 0) {
      const totalEntityQuantity = items.reduce((acc, item) => acc + (item.quantity ?? 0), 0);
      const totalOrderQuantity = orderItemsToPay.reduce((acc, item) => acc + (item.quantity ?? 0), 0);
      return totalEntityQuantity === totalOrderQuantity;
    }
    return false;
  }

  /**
* checkPrescriptionPaymentStatus
* @description Vérifie si tous les éléments de la prescription sont payés
* @param prescriptItems PrescriptionItem[]
* @returns boolean
*/
  public async checkPrescriptionPaymentStatus(prescriptItems: PrescriptionItem[], trx: TransactionClientContract): Promise<boolean> {
    try {
      if (prescriptItems.length === 0) {
        return false; // Aucun élément à vérifier
      }

      // Récupérer les IDs des éléments de prescription
      const prescriptionItemIds = prescriptItems.map(item => item.id);

      // Récupérer les items de commande payés associés aux items de prescription
      const paidItems = await OrderItem.query().useTransaction(trx)
        .whereIn('prescription_item_id', prescriptionItemIds)
        .whereNull('cancelled_at') // Exclure les éléments annulés
        .whereNotNull('paid_at'); // Inclure uniquement les éléments payés

      // Vérifier si chaque élément de la prescription est entièrement payé
      const allItemsPaid = prescriptItems.every(prescriptionItem => {
        // Calculer la quantité totale payée pour cet élément de prescription
        const totalQuantityPaid = paidItems
          .filter(orderItem => orderItem.prescriptionItemId === prescriptionItem.id)
          .reduce((sum, orderItem) => sum + orderItem.quantity, 0);

        // Vérifier si la quantité payée correspond à la quantité prescrite
        return totalQuantityPaid === prescriptionItem.quantity;
      });

      return allItemsPaid;
    } catch (error) {
      console.error("Erreur lors de la vérification du statut de paiement de la prescription :", error);
      throw new Error("Une erreur est survenue lors de la vérification du statut de paiement");
    }
  }



  /**
   * checkAnalyzeAskPaymentStatus
   * @description Vérifie si tous les éléments de l'analyse sont payés
   * @param analyzeAskItems AnalyzeAskItem[]
   * @returns boolean
   */
  public async checkAnalyzeAskPaymentStatus(analyzeAskItems: AnalyzeAskItem[],trx: TransactionClientContract): Promise<boolean> {
    try {
      if (analyzeAskItems.length === 0) {
        return false;
      }

      const analyzeAskItemIds = analyzeAskItems.map(item => item.id);

      const paidItems = await OrderItem.query().useTransaction(trx)
        .whereIn('analyze_ask_item_id', analyzeAskItemIds)
        .whereNull('cancelled_at')
        .whereNotNull('paid_at');

      const allItemsPaid = analyzeAskItems.every(analyzeAskItem => {
        // Vérifier si un item de commande payé correspond à cet élément de l'analyse
        return paidItems.some(orderItem => orderItem.analyzeAskItemId === analyzeAskItem.id);
      });

      return allItemsPaid;
    } catch (error) {
      console.error("Erreur lors de la vérification du statut de paiement de l'analyse :", error);
      throw new Error("Une erreur est survenue lors de la vérification du statut de paiement");
    }
  }

  public async fixQuotationRequestStatus({ response }: HttpContextContract) {
    const quotationRequests = await QuotationRequest.query()
      .where('status', 'partially_paid')
      .preload('analyzeAsk')
      .preload('prescription')
      .preload('orders', (query) => {
        query.where('status', 'paid').preload('items');
      });

    for (const quotationRequest of quotationRequests) {
      const { analyzeAsk, prescription, orders } = quotationRequest;

      if (orders.length > 0) {
        // 1. Vérifier et corriger les prescriptions
        if (prescription) {
          const prescriptItems = await PrescriptionItem.query()
            .where('prescription_id', prescription.id)
            .forUpdate();

          const totalPrescribedQuantity = prescriptItems.reduce((sum, item) => sum + (item.quantity ?? 0), 0);
          const totalPaidQuantity = orders
            .flatMap(order => order.items)
            .filter(item => item.status === 'paid' && item.prescriptionItemId !== null)
            .reduce((sum, item) => sum + (item.quantity ?? 0), 0);

          if (totalPaidQuantity === totalPrescribedQuantity) {
            await prescription.merge({ paymentStatus: 'paid', paymentDate: DateTime.now() }).save();
            await quotationRequest.merge({ status: 'paid' }).save();

            for (const item of prescriptItems) {
              const matchingPaidItem = orders
                .flatMap(order => order.items)
                .find(i => i.prescriptionItemId === item.id);

              if (matchingPaidItem) {
                item.merge({ is_paid: true, paidAt: DateTime.now() });
                await item.save();
              }
            }
          }
        }

        // 2. Vérifier et corriger les analyses
        if (analyzeAsk) {
          const analyzeAskItems = await AnalyzeAskItem.query()
            .where('analyze_ask_id', analyzeAsk.id)
            .forUpdate();

          const totalAnalyzeAskQuantity = analyzeAskItems.length;
          const totalPaidQuantity = orders
            .flatMap(order => order.items)
            .filter(item => item.status === 'paid' && item.analyzeAskItemId !== null)
            .reduce((sum, item) => sum + (item.quantity ?? 0), 0);

          if (totalPaidQuantity === totalAnalyzeAskQuantity) {
            await analyzeAsk.merge({ paymentStatus: 'paid' }).save();
            await quotationRequest.merge({ status: 'paid' }).save();

            for (const item of analyzeAskItems) {
              const matchingPaidItem = orders
                .flatMap(order => order.items)
                .find(i => i.analyzeAskItemId === item.id);

              if (matchingPaidItem) {
                item.merge({ isPaid: true, paidAt: DateTime.now() });
                await item.save();
              }
            }
          }
        }
      }
    }

    return response.status(200).json({
      success: true,
      message: "Payment request statuses fixed successfully",
      result: quotationRequests,
    });
  }

  public async getappointmentAmount(services: number[] | null | undefined, appointmentTypeId: number) {
    let amount = 0;
    if (services && services.length > 0) {
      for (const service of services) {
        const req = await Service.query().where('id', service).first();
        if (!req) {
          return null;
        }
        amount += req.price;
      }
    }
    const type = await AppointmentType.query().where('id', appointmentTypeId).first();
    if (type) {
      let amountPaid = type.pricings.amount_paid;
      amount += amountPaid;
    }
    return amount;
  }

  public async addQrcodeParrainage(trx: TransactionClientContract, code_parrainage: string, userParrainedId: number, last_name?: string, first_name?: string) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
    }
    try {
      const parrainUser = await User.query().where('code_parrainage', code_parrainage).forUpdate().first();
      if (parrainUser) {
        let parrainage = parrainUser.parrainage as UserParrainage;
        let settings = await ParrainageSetting.query().where('id', parrainage.plan).first();
        if (settings && parrainage) {
          let qr_setting = settings.metadata.find(m => m.key === 'qrcode');

          if (qr_setting) {
            const Newparrainage = await Parrainage.create({
              parrainId: parrainUser.id,
              userParrainedId: userParrainedId,
              codeParrainage: code_parrainage,
              type: 'QRCODE',
              lastName: last_name,
              firstName: first_name,
              phone: parrainUser.phone,
            }, { client: trx });

            if (Newparrainage) {
              parrainage = {
                ...parrainage,
                active_qrcode: parrainage.active_qrcode + 1,
              }
              parrainUser.parrainage = JSON.stringify(parrainage);
              await parrainUser.useTransaction(trx).save();

              //active monetization
              if (parrainage.activeMoney) {
                const monetization = await Monetization.query().where('user_id', userParrainedId).forUpdate().first();
                if (monetization) {
                  let old_balance = monetization.balance as MonetizationBalance;
                  let newSolde = old_balance.solde + qr_setting.unit_price;
                  let newBalance = {
                    solde: newSolde,
                    currency: old_balance.currency,
                    last_update: new Date().toISOString(),
                  }

                  monetization.balance = newBalance;
                  await monetization.useTransaction(trx).save();
                }
              }
              apiResponse = {
                success: true,
                message: 'Parrainage ajouté avec succès',
                result: Newparrainage,
              }
            } else {
              apiResponse = {
                success: false,
                message: "Parrainage non crée",
                result: null,
                except: Newparrainage
              }
            }
          } else {
            apiResponse = {
              success: false,
              message: "Parrainage non crée",
              result: null,
              except: qr_setting
            }
          }
        }
      } else {
        apiResponse = {
          success: false,
          message: "Code de parrainage incorrecte",
          result: null
        }
      }

    } catch (error) {
      console.log(error);
      apiResponse = {
        success: false,
        message: "Une erreur est survenue lors du parrainage du compte",
        result: null,
      }
    }
    return apiResponse;
  }

  public async getPatientActiveInsuranceSubscription(patientInsuranceId: number): Promise<InsuranceCompanySubscription | null> {
    const query = await InsuranceCompanySubscription.query().where('patient_insurance_company_id', patientInsuranceId).where('status', 'active').preload('package').first();
    return query ? query : null;
  }

  public async getInsuranceCompanyWallet(insuranceIAgencyd: number): Promise<Wallet | null> {
    const query = await Wallet.query().where('owner_type', 'agency').where('owner_id', insuranceIAgencyd).first();
    return query ? query : null;
  }

  public async generateCodeParrainage(length: number) {
    const chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    let codeParrainage = '';
    for (let i = 0; i < length; i++) {
      codeParrainage += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return codeParrainage.toUpperCase();
  }

  public async generateToken() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
      const r = (Math.random() * 16) | 0;
      const v = c === 'x' ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  }

  // Fonction utilitaire pour mélanger les caractères d'une chaîne
  private shuffleString(input: string): string {
    let array = input.split('');
    for (let i = array.length - 1; i > 0; i--) {
      let j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]]; // Échange des éléments
    }
    return array.join('');
  }

  public async generateChannel(username: string) {
    let cleanedUsername = username.replace(/\s+/g, '').toLowerCase();
    let shuffledUsername = this.shuffleString(cleanedUsername);
    let randomPart = Math.random().toString(36).substring(2, 18);
    let timestamp = Date.now().toString();
    let uniqueChannel = `${randomPart}-${timestamp}-${shuffledUsername}`;

    return uniqueChannel;
  }


  // Fonction pour valider le wallet et le solde
  public async validateWalletAndBalance(userId: number, paymentTypeName: string): Promise<{ success: boolean; message: string; statusCode: number; wallet?: any; fees?: number }> {
    const wallet = await this.getWalletByUserId(userId);

    if (!wallet) {
      return { success: false, message: "Portefeuille non trouvé", statusCode: 404 };
    }

    const paymentType = await this.getPaymentTypeByName(paymentTypeName);

    if (!paymentType || !paymentType.configs?.amount) {
      return { success: false, message: "Type de paiement introuvable", statusCode: 400 };
    }

    const fees = Number(paymentType.configs.amount);

    if (wallet.balance < fees) {
      return { success: false, message: "Solde insuffisant", statusCode: 400 };
    }

    return { success: true, message: "", statusCode: 200, wallet, fees };
  }

  // Fonction pour valider la carte
  public async validateCard(card_uuid: string, trx: any): Promise<{ success: boolean; message: string; card?: Card }> {
    const card = await Card.query()
      .useTransaction(trx)
      .where('type', 'physical')
      .where('uid', card_uuid)
      .where('is_used', false)
      .andWhere('status', 'new')
      .forUpdate()
      .first();

    if (!card) {
      return { success: false, message: "Cette carte n'existe pas ou est déjà utilisée." };
    }

    if (card.status === 'blocked') {
      return { success: false, message: "Carnet bloqué." };
    }

    if (card.status === 'used') {
      return { success: false, message: "Carnet déjà en cours d'utilisation." };
    }

    return { success: true, message: "", card };
  }




  public async updateCardOrderStock(card: Card, trx: TransactionClientContract) {
    if (!card) return false;

    const order = await CardOrder.query().where('id', card.cardOrderId).first();
    if (!order) return false;

    let card_stock: CardStock = order.stock;

    // Choisir le type de stock (online ou physical) à mettre à jour
    const stockType = card.type === 'online' ? 'online_quantity' : 'physical_quantity';

    // Calcul de la nouvelle quantité
    const updatedStock = {
      ...card_stock,
      [stockType]: {
        ...card_stock[stockType],
        current: card_stock[stockType].current - 1, // Décrémenter la quantité courante
      },
    };

    // Mise à jour du stock et sauvegarde de la commande
    order.stock = JSON.stringify(updatedStock);
    await order.useTransaction(trx)
      .merge({
        stock: order.stock,
      })
      .save();
    return true; // Mise à jour réussie
  }

  public async fixInsuranceSubscriptionTotalAmountPaid({ response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    };
    try {
      const subscriptions = await InsuranceCompanySubscription.all();
      const trx = await Database.transaction();
      let data = [] as any[];
      for (const subscription of subscriptions) {
        const insuranceFees = await InsuranceCompanyFee.query().where('insurance_company_subscription_id', subscription.id);
        let totalAmountPaid = 0;
        for (const fee of insuranceFees) {
          if (!fee.paidByTranche) {
            totalAmountPaid += Number(fee.amountPaid);
          } else {
            let tranches: PaymentTranche[] = [];
            if (typeof fee.tranches === 'string') {
              try {
                tranches = JSON.parse(fee.tranches) as PaymentTranche[];
              } catch (error) {
                console.error("Erreur lors du parsing des tranches :", error);
              }
            } else if (Array.isArray(fee.tranches)) {
              tranches = fee.tranches as PaymentTranche[];
            }

            for (const tranche of tranches) {
              if (tranche.status === 'paid') {
                totalAmountPaid += tranche.amount;
              }
            }
          }
        }
        subscription.totalAmountPaid = Number(totalAmountPaid.toFixed(2));
        await subscription.useTransaction(trx).save();

        data.push({
          id: subscription.id,
          totalAmountPaid: subscription.totalAmountPaid,
          isUpToDate: subscription.isUpToDate,
          nextDeadline: subscription.nextDeadline,
        });
      }

      await trx.commit();
      apiResponse = {
        success: true,
        message: "Mise à jour des cotisations effectuée avec succès",
        result: data,
      };
      return response.status(200).json(apiResponse);

    } catch (error) {
      console.log("error in fixInsuranceSubscriptionTotalAmountPaid", error);
      apiResponse = {
        success: false,
        message: "Erreur lors de la mise à jour des cotisations",
        result: null as any,
        except: error.message
      };
      return response.status(500).json(apiResponse);
    }
  }

  public async getActiveYear(insuranceId: number): Promise<InsuranceYear | null> {
    const year = await InsuranceYear.query().where('insurance_company_id', insuranceId).where('status', 'started').first();
    if (!year) return null;
    return year;
  }




}
