ARG NODE_IMAGE=node:20.11.1-alpine

FROM $NODE_IMAGE AS base

WORKDIR /home/<USER>/app

# Mettre à jour npm pour éviter les problèmes de compatibilité
RUN npm install -g npm@10.2.4

# Copier les fichiers package.json et yarn.lock dans le conteneur
COPY package.json package-lock.json ./

# Installer les dépendances avec npm avec des options supplémentaires pour la stabilité
RUN npm ci --quiet --no-audit --no-fund --network-timeout 100000

# Copier le reste des fichiers de l'application
COPY . .

# Définir les variables d'environnement pour le projet
ARG HOST
ARG PORT
ENV HOST=$HOST
ENV PORT=$PORT

# Étape de développement
FROM base AS dev
ENV CHOKIDAR_USEPOLLING=true
ENV NODE_ENV=development
CMD ["node", "ace", "serve", "--watch", "--node-args=--inspect=0.0.0.0"]
