import Route from '@ioc:Adonis/Core/Route';
import PaymentIntentController from 'App/Controllers/Http/api/core/PaymentIntentController';

const intentCtrl = new PaymentIntentController();
Route.group(() => {
  Route.group(() => {
    Route.post('/wallet/init-deposit',async(ctx)=>{
      return intentCtrl.initWalletDeposit(ctx);
    });
    Route.post('/intent/check-status', async (ctx) => {
      return intentCtrl.checkWalletIntent(ctx);
    });
    Route.any('/intent/callback', async (ctx) => {
      return intentCtrl.intentCallback(ctx);
    });
  }).prefix('payments').middleware('auth-user');
}).prefix('api').namespace('App/Controllers/Http/api/core');
