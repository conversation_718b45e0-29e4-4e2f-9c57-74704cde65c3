import Route from '@ioc:Adonis/Core/Route';
import TransactionController from 'App/Controllers/Http/api/core/TransactionController';

const transactCtrl = new TransactionController();
Route.group(() => {
  Route.group(() => {
    Route.get('/', async (ctx) => {
      return transactCtrl.getTransactions(ctx);
    });
    Route.get('/payer', async (ctx) => {
      return transactCtrl.getTransactionsByPayer(ctx);
    });
    Route.post('/pay-qrcode', async (ctx) => {
      return transactCtrl.payQrcodeByWallet(ctx);
    });

    Route.post('/pay-order', async (ctx) => {
      return transactCtrl.payOrder(ctx);
    });
    Route.post('/pay-appointment', async (ctx) => {
      return transactCtrl.AddAppointmentPayment(ctx);
    });
    Route.post('/insurances/subscriptions-fees/add', async (ctx) => {
      return transactCtrl.addInsuranceSubscriptionPayment(ctx);
    });
    Route.post('/insurances/cotisations-fees/add', async (ctx) => {
      return transactCtrl.payInsuranceFees(ctx);
    });
    Route.post('/insurances/beneficiaries/pay-qrcode', async (ctx) => {
      return transactCtrl.payQrcodeForBeneficiary(ctx);
    });
  }).prefix('transactions').middleware('auth-user');
}).prefix('api').namespace('App/Controllers/Http/api/core');
