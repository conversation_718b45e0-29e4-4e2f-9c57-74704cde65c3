import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class Soignant extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'user_id' })
  public userId: number

  @column({ columnName: 'last_name' })
  public lastName: string

  @column({ columnName: 'first_name' })
  public firstName: string

  @column({ columnName: 'phone' })
  public phone: string

  @column({ columnName: 'email' })
  public email: string

  @column({ columnName: 'address' })
  public address: string

  @column({ columnName: 'country_id' })
  public countryId: number

  @column({ columnName: 'city_id' })
  public cityId: number

  @column({ columnName: 'quarter_id' })
  public quarterId: number

  @column({ columnName: 'gender' })
  public gender: string

  @column({ columnName: 'profession' })
  public profession: string

  @column({ columnName: 'birthday_year' })
  public birthdayYear: number

  @column({ columnName: 'birthday_month' })
  public birthdayMonth: number

  @column({ columnName: 'birthday_day' })
  public birthdayDay: number

  @column({ columnName: 'domain_id' })
  public domainId: number

  @column({ columnName: 'departement' })
  public departement: string

  @column({ columnName: 'docs' })
  public docs: string

  @column({ columnName: 'health_institute_id' })
  public healthInstituteId: number

  @column({ columnName: 'status' })
  public status: string

  @column({ columnName: 'schedules' })
  public schedules: string

  @column({ columnName: 'code' })
  public code: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
