import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, BelongsTo, hasMany, HasMany } from '@ioc:Adonis/Lucid/Orm'
import AnalyzeAsk from './AnalyzeAsk'
import InsuranceCompany from './InsuranceCompany'
import Patient from './Patient'
import PatientInsuranceCompany from './PatientInsuranceCompany'
import InsuranceCompanyAnalyzeAskItem from './InsuranceCompanyAnalyzeAskItem'
import Diagnostic from './Diagnostic'

export default class InsuranceCompanyAnalyzeAsk extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'analyze_ask_id' })
  public analyzeAskId: number

  @column({ columnName: 'diagnostic_id' })
  public diagnosticId: number

  @column({ columnName: 'insurance_company_id' })
  public insuranceCompanyId: number

  @column({ columnName: 'patient_id' })
  public patientId: number

  @column({ columnName: 'patient_insurance_company_id' })
  public patientInsuranceCompanyId: number

  @column({ columnName: 'total_items_assured' })
  public totalItemsAssured: number

  @column({ columnName: 'total_amount_assured' })
  public totalAmountAssured: number

  @column({ columnName: 'status' })
  public status: 'pending' | 'validated' | 'blocked' | 'rejected'

  @column({ columnName: 'validated_by' })
  public validatedBy: number | null

  @column({ columnName: 'notes' })
  public notes: string | null

  @column({ columnName: 'rejected_reason' })
  public rejectedReason: string | null

  @column.dateTime({ columnName: 'validated_at' })
  public validatedAt: DateTime | null

  @column.dateTime({ columnName: 'blocked_at' })
  public blockedAt: DateTime | null

  @column.dateTime({ columnName: 'rejected_at' })
  public rejectedAt: DateTime | null

  @column.dateTime({ autoCreate: true, columnName: 'created_at' })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true, columnName: 'updated_at' })
  public updatedAt: DateTime

  // Relations
  @belongsTo(() => AnalyzeAsk, {
    foreignKey: 'analyzeAskId',
  })
  public analyzeAsk: BelongsTo<typeof AnalyzeAsk>

  @belongsTo(() => InsuranceCompany, {
    foreignKey: 'insuranceCompanyId',
  })
  public insuranceCompany: BelongsTo<typeof InsuranceCompany>

  @belongsTo(() => Patient, {
    foreignKey: 'patientId',
  })
  public patient: BelongsTo<typeof Patient>

  @belongsTo(() => Diagnostic, {
    foreignKey: 'diagnosticId'
  })
  public diagnostic: BelongsTo<typeof Diagnostic>

  @belongsTo(() => PatientInsuranceCompany, {
    foreignKey: 'patientInsuranceCompanyId',
  })
  public patientInsuranceCompany: BelongsTo<typeof PatientInsuranceCompany>

  @hasMany(() => InsuranceCompanyAnalyzeAskItem, {
    foreignKey: 'insuranceCompanyAnalyzeAskId',
  })
  public items: HasMany<typeof InsuranceCompanyAnalyzeAskItem>
}
