export interface ApiResponse {
  success: boolean
  message: string
  result: any
  except?: any
  errors?: any
}

export interface Address  {
  libelle?: string,
  lat?: string,
  long?: string,
}

export interface Contact{
  name?: string;
	phone?: string;
	email?: string;
}

export interface PaymentOrder {
  amount: number;
  merchant_reference: string;
  description: string;
  callback_url?: string;
  redirect_url?: string;
  type_notif?: ('sms' | 'email')[];
  gateway_id?: number;
  client: OrderClient;
}

export interface PaymentRequest {
  order_reference: string;
  amount: number;
  state: string;
  date_create: string;
  bill_url: string;
  code: string;
  merchant_reference: string;
  client: OrderClient;
  received_amount: number;
  callback_url: string;
  callback_type: null;
  callback_id: null;
  redirect_url: null;
  currency: string;
  ledger: null;
  payment_status: string;
  status: string;
  message: string;
  qrcode_url: string;
  payments_method: PaymentMethod[];
}

interface PaymentMethod {
  gateway: string;
  method: string;
  action: string;
  description: string;
  reference: string;
  id: number;
}

export interface OrderClient {
  lastname: string;
  firstname: string;
  email: string | null;
  phone: string;
  city?: string;
  country?: string;
  address1?: string;
  address2?: string;
}

export interface PaymentResponse {
  state: 'Pending'|'Error' | 'Paid' |'Partial' | 'Excess';
  order_reference: string;
  merchant_reference: string;
  amount: number;
  currency: string;
  billed_amount: number;
  bill_url: string;
  date_update: string | null;
  date_expiration: string;
  client: {
    phone: string;
  };
  code_paiement: string;
  date_create: string;
  callback_status: string;
  payments: any[]; // ou spécifiez un type si vous avez plus d'informations
}

export const credential = {
  "login": "b.atabu",
  "password": "password",
  "username": "DokitaEyes",
  "client_id": "123456789",
  "client_secret": "********",
  "api_reference": "136"
}

export interface PaymentCallbackRequest {
  order_reference: string;
  merchant_reference: string;
  state: 'Pending'|'Error' | 'Paid' |'Partial' | 'Excess';
  identifier: string;
  bill_url: string;
  client: OrderClient;
  received_amount: number;
  status?: string;
  message?: string;
  qrcode_url: string;
  payments: PaymentMethod[];
}

export interface UserParrainage{
  plan: number
  activeMoney: boolean
  active_qrcode: number
  adhesion_fees: number
  create_account: number
}


export interface ParrainageMetadata {
  libelle: string;
  description: string;
  value: number;
  key: string;
  required: boolean;
  unit_price: number;
}

export type MonetizationBalance = {
  solde: number;
  currency: string;
  last_update: string;
}

export interface PaymentTranche {
  id: number;
  amount: number;
  transaction_id: number | null;
  token: string | null;
  paid_at: Date | null;
  reference: string | null;
  method: 'mobile_money' | 'wallet';
  status: 'paid' | 'pending' | 'error';
}

export interface NewSubscriptionData {
  subscription_id: number;
  insurance_year_id: number;
  package_id: number;
  patient_id: number;
}


export enum InsuranceFeeStatus {
  Pending = 'pending',
  Paid = 'paid',
  Completed = 'completed',
  Cancelled = 'cancelled',
}

export enum UserStatus {
  Pending = 'pending',
  Actived = 'actived',
  Blocked = 'blocked',
  Deleted = 'deleted',
  Inactive = 'inactive',
  Archived = 'archived',
  SendIdentity = 'send_identity',
  ValidatedIdentity = 'validated_identity',
  RejectedIdentity = 'rejected_identity',
}


export enum PatientStatus {
  Activated = 'activated',
  Validated = 'validated',
  Rejected = 'rejected',
  Archived = 'archived',
  Blocked = 'blocked',
  Pending = 'pending',
  Kyc = 'kyc',
}

export enum YearStatus {
  Pending = 'pending',
  Active = 'active',
  Started = 'started',
  Finished = 'finished',
  Expired = 'expired',
}

export enum PackageStatus {
  Draft = 'draft',
  Configured = 'configured',
  Published = 'published',
  Archived = 'archived',
}

export interface CardStock {
  online_quantity: {
      initial: number,
      current: number,
  },
  physical_quantity: {
      initial: number,
      current: number,
  }
}

export interface VersionItem {
  item_id: number;
  quantity: number;
  type: 'prescription' | 'analyze';
}
export interface QuotationVersion {
  version_id: number;
  created_at: string;
  paid_at?: string;
  status: 'pending' | 'approved' | 'rejected' | 'expired'| 'cancelled' | 'paid' | 'partially_paid' | 'completed';
  items: VersionItem[];
}
