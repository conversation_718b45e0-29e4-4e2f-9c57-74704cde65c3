import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

import { schema, rules } from '@ioc:Adonis/Core/Validator';
import { formatErrorResponse } from "App/Controllers/Utils";
import { ApiResponse, InsuranceFeeStatus, NewSubscriptionData, PatientStatus, PaymentTranche, QuotationVersion, UserStatus } from "App/Controllers/Utils/models";
import HelperController from '../helpers/HelperController';
import Database from '@ioc:Adonis/Lucid/Database';
import WalletTrait from "App/Controllers/Http/api/traits/WalletTrait";
import Transaction from 'App/Models/Transaction';
import Card from 'App/Models/Card';
// import Patient from 'App/Models/Patient';
import { DateTime } from 'luxon';
import Code from 'App/Models/Code';
import Pharmacy from 'App/Models/Pharmacy';
import Laboratory from 'App/Models/Laboratory';
// import Prescription from 'App/Models/Prescription';
import Wallet from 'App/Models/Wallet';
import PrescriptionItem from 'App/Models/PrescriptionItem';
import AnalyzeAskItem from 'App/Models/AnalyzeAskItem';
import Order from 'App/Models/Order';
import Appointment from 'App/Models/Appointment';
import PatientInsuranceCompany from 'App/Models/PatientInsuranceCompany';
import InsuranceCompanySubscription from 'App/Models/InsuranceCompanySubscription';
import Package from 'App/Models/Package';
import InsuranceCompanyFee from 'App/Models/InsuranceCompanyFee';
import InsuranceCompanyBeneficiary from 'App/Models/InsuranceCompanyBeneficiary';
import Patient from 'App/Models/Patient';
import InsuranceCompany from 'App/Models/InsuranceCompany';
import { TransactionClientContract } from '@ioc:Adonis/Lucid/Database';
import User from 'App/Models/User';
import QuotationProposalItem from 'App/Models/QuotationProposalItem';
import QuotationPartner from 'App/Models/QuotationPartner';
import NatService from 'App/Services/NatService';
// import OrderItem from 'App/Models/OrderItem';


export default class TransactionController extends HelperController {


  public async getTransactions({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    }
    let token = request.header('dotoken');
    console.log("token", token);

    try {
      const userId = (await this.verifyJWT(String(token))).user_id as number;
      const user = await this.getUserById(userId);
      if (!user) {
        apiResponse = {
          success: false,
          message: "Aucun utilisateur trouvé",
          result: null as any,
          except: null
        }
        return response.status(404).json(apiResponse);
      }
      const page = request.input('page') || 1;
      const limit = request.input('limit') || 10;
      const transacts = await Transaction.query().where('user_id', userId).orderBy('created_at', 'desc')
        .preload('beneficiary')
        .preload('user')
        .preload('payer')
        .preload('paymentType')
        .paginate(page, limit);

      apiResponse = {
        success: true,
        message: "Transactions récupérées avec succès",
        result: transacts,
      }
    } catch (error) {
      console.log("error in get transactions", error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
    }
    return response.status(200).json(apiResponse);
  }


  /**
   * Récupérer les transactions effectuées par un utilisateur
   * @param param0
   * @returns
   */
  public async getTransactionsByPayer({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    }
    let token = request.header('dotoken');
    try {
      const userId = (await this.verifyJWT(String(token))).user_id as number;
      const user = await this.getUserById(userId);
      if (!user) {
        apiResponse = {
          success: false,
          message: "Aucun utilisateur trouvé",
          result: null as any,
          except: null
        }
        return response.status(404).json(apiResponse);
      }
      const page = request.input('page') || 1;
      const limit = request.input('limit') || 10;
      const transacts = await Transaction.query().where('payer_id', userId).orderBy('created_at', 'desc')
        .preload('beneficiary')
        .preload('user')
        .preload('payer')
        .paginate(page, limit);

      apiResponse = {
        success: true,
        message: "Transactions récupérées avec succès",
        result: transacts,
      }
      return response.status(200).json(apiResponse);
    } catch (error) {
      console.log("error in get transactions by payer", error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null as any,
        except: error
      }
      return response.status(200).json(apiResponse);
    }
  }

  public async payQrcodeByWallet({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    };

    let token = request.header('dotoken');
    try {
      const code_parrainage = request.input('code_parrainage');
      const user_id = (await this.verifyJWT(String(token))).user_id as number;
      // Récupérer l'utilisateur et le patient
      const user = await this.getUserById(user_id);
      if (!user) {
        apiResponse.message = "Utilisateur non trouvé";
        return response.status(404).json(apiResponse);
      }

      const patient = await this.getPatientByUserId(user_id);
      if (!patient || patient.carnet_is_active) {
        apiResponse.message = patient ? "Le carnet est déjà activé" : "Patient non trouvé";
        return response.status(400).json(apiResponse);
      }

      // Récupérer le portefeuille et vérifier le solde
      const wallet = await this.getWalletByUserId(user_id);
      if (!wallet) {
        apiResponse.message = "Portefeuille non trouvé";
        return response.status(404).json(apiResponse);
      }

      const paymentType = await this.getPaymentTypeByName('QRCODE');
      if (!paymentType || !paymentType.configs?.amount) {
        apiResponse.message = "Type de paiement introuvable";
        return response.status(400).json(apiResponse);
      }

      const qrcodeFees = Number(paymentType.configs.amount);
      if (wallet.balance < qrcodeFees) {
        apiResponse.message = "Solde insuffisant";
        return response.status(400).json(apiResponse);
      }

      // Sélectionner une carte disponible (avec verrouillage)
      const card = await Card.query()
        .where('is_paid', false)
        .andWhere('is_used', false)
        .andWhere('status', 'new')
        .andWhere('type', 'online')
        .forUpdate()
        .first();

      if (!card) {
        apiResponse.message = "Aucune carte disponible";
        return response.status(400).json(apiResponse);
      }

      // Créer la transaction de paiement
      // Démarrer une nouvelle transaction
      let trx = await Database.transaction();
      try {
        const trxRef = await this.generateUUID();
        const description = "Vous avez effectué avec succès une transaction de paiement de la carte QRCODE";
        const metadata = {
          cardId: card.id,
          paymentType: paymentType.name,
          amount: qrcodeFees,
          patient: `${patient.last_name} ${patient.first_name}`,
        };

        const transact = await Transaction.create({
          userId: user_id,
          beneficiaryId: patient.id,
          paidBySelf: true,
          walletId: wallet.id,
          amount: qrcodeFees,
          description: description,
          status: "pending",
          paymentTypeId: paymentType.id,
          dateOp: new Date(),
          trxRef: trxRef,
          metadata: metadata,
          balances: {
            last_balance: wallet.balance,
            new_balance: wallet.balance - qrcodeFees,
          },
        }, { client: trx });

        if (!transact) {
          await trx.rollback();
          apiResponse = {
            success: false,
            message: "Erreur lors de la création de la transaction",
            result: null,
            except: transact
          };
          return response.status(200).json(apiResponse);
        }
        // Débiter le portefeuille en passant la transaction active
        const trait = new WalletTrait();
        const debite = await trait.debiteWallet(wallet, qrcodeFees, "payment", transact.id, trx);
        if (!debite.success) {
          console.log("error in debite wallet", debite);
          await trx.rollback();
          apiResponse = {
            success: false,
            message: debite.message,
            result: null,
            except: debite
          };
          return response.status(200).json(apiResponse);
        }

        // Créer le code associé à la carte
        const validity = 365;
        const newCode = await Code.create({
          patientId: patient.id,
          cardId: card.id,
          validity: validity.toString(),
          activatedAt: DateTime.now().toISODate(),
          expiredAt: DateTime.now().plus({ days: validity }).toISODate(),
          onlinePaid: 1,
        }, { client: trx });

        if (!newCode) {
          await trx.rollback();
          apiResponse = {
            success: false,
            message: "Erreur lors de la création du code",
            result: null,
            except: newCode
          };
          return response.status(200).json(apiResponse);
        }

        // Mettre à jour la carte et le patient
        await card.merge({ isUsed: true, isPaid: true, status: 'used' }).useTransaction(trx).save();
        await patient.merge({ carnet_is_active: true }).useTransaction(trx).save();

        // Parrainage
        if (code_parrainage) {
          const parrainage = await this.addQrcodeParrainage(trx, code_parrainage, user_id, patient.last_name, patient.first_name);
          if (!parrainage.success) {
            console.log("error in add qr code parrainage", parrainage);
            await trx.rollback();
            apiResponse = {
              success: false,
              message: parrainage.message,
              result: null,
              except: parrainage
            };
            return response.status(200).json(apiResponse);
          }
        }

        // Valider la transaction
        transact.status = "paid";
        await transact.useTransaction(trx).save();

        await trx.commit();

        // Mettre à jour la réponse API
        apiResponse = {
          success: true,
          message: "Paiement de la carte effectué avec succès",
          result: {
            patient: patient,
            card: card,
            code: newCode,
            wallet: wallet,
            transact: transact,
          },
          except: null
        };

      } catch (error) {
        await trx.rollback();
        console.log("error in pay qrcode by wallet", error);
        apiResponse = {
          success: false,
          message: formatErrorResponse(error),
          result: null,
          except: error.message
        };
      }
    } catch (error) {
      console.log("error in pay qrcode by wallet", error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null,
        except: error.message
      };
    }
    return response.status(200).json(apiResponse);
  }



  /**
   * payOrder function
   * @description Payer une commande
   * @param request: HttpContextContract
   * @returns
   */
  public async payOrder({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    };
    let status = 201;
    let token = request.header('dotoken');

    try {
      const user_id = (await this.verifyJWT(String(token))).user_id as number;
      const payload = await request.validate({
        schema: schema.create({
          orderId: schema.number(),
          amount: schema.number(),
          payment_type: schema.enum([2, 3])
        })
      });
      const { payment_type, orderId, amount } = payload;

      // Récupérer l'utilisateur et le patient
      const user = await this.getUserById(user_id);
      if (!user) {
        apiResponse.message = "Utilisateur non trouvé";
        return response.status(404).json(apiResponse);
      }

      const patient = await this.getPatientByUserId(user_id);
      if (!patient) {
        apiResponse.message = "Patient non trouvé";
        return response.status(400).json(apiResponse);
      }

      // Récupérer le portefeuille et vérifier le solde
      const wallet = await this.getWalletByUserId(user_id);
      if (!wallet) {
        apiResponse.message = "Portefeuille non trouvé";
        return response.status(404).json(apiResponse);
      }

      // Charger la commande et ses éléments associés
      const order = await Order.query()
        .where('id', orderId)
        .andWhere('patient_id', patient.id)
        .preload('items')
        .preload('prescription')
        .preload('analyze_ask')
        .preload('quotation_request')
        .preload('proposition')
        .forUpdate()
        .first();

      if (!order) {
        apiResponse.message = "Commande non trouvée";
        return response.status(404).json(apiResponse);
      }

      let entity: Pharmacy | Laboratory | null = null;
      const prescription = order.prescription;
      const analyzeAsk = order.analyze_ask;

      // Vérifier si la commande est liée à une prescription
      let pharmacyId: number | null = null;
      let laboratoryId: number | null = null;
      if (order.prescriptionId !== null && payment_type === 2) {
        pharmacyId = Number(order.pharmacyId);
        entity = await Pharmacy.query().where('id', pharmacyId).first();
      }
      else if (order.analyzeAskId !== null && payment_type === 3) {
        laboratoryId = Number(order.laboratoryId);
        entity = await Laboratory.query().where('id', laboratoryId).first();
      }

      if (!entity) {
        apiResponse.message = "Aucune entité associée trouvée pour cette commande";
        return response.status(404).json(apiResponse);
      }

      // Créditer le portefeuille de l'entité
      const walletEntity = await Wallet.query().where('owner_id', entity.id).andWhere('type_wallet_id', 3).andWhere('owner_type', pharmacyId ? 'pharmacy' : 'laboratory').forUpdate().first();
      if (!walletEntity) {
        apiResponse.message = "Aucun portefeuille trouvé pour cette entité";
        return response.status(404).json(apiResponse);
      }

      const orderItems = order.items;
      const orderItemsToPay = orderItems.filter(item => item.status === 'pending');

      if (orderItemsToPay.length === 0) {
        apiResponse.message = "Tous les éléments sont payés ou expédiés";
        return response.status(400).json(apiResponse);
      }

      const orderItemsToPayTotal = orderItemsToPay.reduce((acc, item) => acc + (item?.totalPrice ?? 0), 0);

      if (wallet.balance < orderItemsToPayTotal) {
        apiResponse.message = "Solde insuffisant,veuillez recharger votre compte afin de poursuivre la transaction";
        return response.status(400).json(apiResponse);
      }

      const quotationRequest = order.quotation_request;

      if (!quotationRequest) {
        apiResponse.message = "Aucune demande de devis trouvée pour cette commande";
        return response.status(400).json(apiResponse);
      }

      const proposal = order.proposition;
      if (!proposal) {
        apiResponse.message = "Aucune proposition trouvée pour cette commande";
        return response.status(400).json(apiResponse);
      }

      const trx = await Database.transaction();

      try {
        const trxRef = await this.generateUUID();
        const metadata = {
          orderId: order.id,
          paymentType: payment_type,
          amount: orderItemsToPayTotal,
          patient: `${patient.last_name} ${patient.first_name}`,
        };


        const trait = new WalletTrait();
        if (walletEntity && orderItemsToPayTotal > 0) {
          const transact = await Transaction.create({
            userId: user_id,
            beneficiaryId: patient.id,
            paidBySelf: true,
            walletId: wallet.id,
            amount: orderItemsToPayTotal,
            description: "Vous avez effectué avec succès une transaction de paiement de la commande " + order.reference,
            status: "pending",
            paymentTypeId: payment_type,
            dateOp: new Date(),
            trxRef: trxRef,
            metadata: metadata,
            balances: {
              last_balance: wallet.balance,
              new_balance: wallet.balance - amount,
            },
          }, { client: trx });

          if (!transact) {
            await trx.rollback();
            apiResponse = {
              success: false,
              message: "Erreur lors de la création de la transaction",
              result: null,
              except: transact
            };
            return response.status(200).json(apiResponse);
          }

          const totalPay = Number(order.totalPrice);
          const transfer = await trait.transferWallet(wallet, walletEntity, totalPay, 'payment', transact.id, trx);
          if (!transfer.success) {
            console.log("Erreur dans transfer wallet", transfer);
            await trx.rollback();
            apiResponse = {
              success: false,
              message: transfer.message,
              result: null,
              except: transfer
            };
            return response.status(400).json(apiResponse);
          }
          let paid_at = DateTime.now().toISO();

          // Mise à jour des statuts de commande
          order.status = "paid";
          order.paidAt = paid_at.toString();
          await order.useTransaction(trx).save();

          // Mise à jour des items de commande
          for (const item of orderItems) {
            if (orderItemsToPay.find(i => i.id === item.id)) {
              item.paidAt = paid_at;
              item.status = "paid";
              await item.useTransaction(trx).save();
            }
          }


          if (prescription) {
            const prescriptItems = await PrescriptionItem.query().useTransaction(trx)
              .where('prescription_id', prescription.id)
              .forUpdate()
              .exec();

            const partner = await QuotationPartner.query().useTransaction(trx).where('quotation_request_id', quotationRequest.id).andWhere('pharmacy_id', Number(proposal.pharmacyId)).first();
            if (!partner) {
              await trx.rollback();
              apiResponse.message = "Aucun partenaire trouvé pour cette commande";
              return response.status(400).json(apiResponse);
            }

            for (const item of prescriptItems) {
              const orderPrescriptionItem = orderItemsToPay.find(i => i.prescriptionItemId === item.id);
              if (orderPrescriptionItem) {
                const proposalItem = await QuotationProposalItem.query().useTransaction(trx).where('quotation_proposal_id', proposal.id).where('prescription_item_id', item.id).first();
                if (proposalItem) {
                  await item.merge({
                    is_paid: true,
                    paidAt: DateTime.now(),
                  }).useTransaction(trx).save();

                  await proposalItem.merge({
                    paidAt: DateTime.now(),
                    quantityPaid: orderPrescriptionItem.quantity,
                  }).useTransaction(trx).save();
                }
              }
            }

            const isPrescriptionPaid = await this.checkPrescriptionPaymentStatus(prescriptItems, trx);
            const versionId = Number(quotationRequest.currentVersion);
            let versions: QuotationVersion[] = typeof quotationRequest.versions === 'string' ? JSON.parse(quotationRequest.versions as string) : quotationRequest.versions;

            let currentVersion: QuotationVersion | undefined = versions.find(version => version.version_id === versionId);
            if (currentVersion) {
              currentVersion.status = 'completed' as 'completed';
              currentVersion.paid_at = DateTime.now().toISO();
            }

            await prescription.merge({
              paymentStatus: isPrescriptionPaid ? 'paid' : 'partially_paid',
              paymentDate: DateTime.now(),
            }).useTransaction(trx).save();

            await quotationRequest.merge({
              status: isPrescriptionPaid ? 'paid' : 'partially_paid',
              versions: JSON.stringify(versions),
            }).useTransaction(trx).save();

            await partner.merge({
              status: isPrescriptionPaid ? 'completed' : 'partially_end',
              completedAt: DateTime.now(),
            }).useTransaction(trx).save();
          }

          if (analyzeAsk) {
            const analyzeAskItems = await AnalyzeAskItem.query().useTransaction(trx)
              .where('analyze_ask_id', analyzeAsk.id)
              .forUpdate();

            const partner = await QuotationPartner.query().useTransaction(trx).where('quotation_request_id', quotationRequest.id).andWhere('laboratory_id', Number(proposal.laboratoryId)).first();
            if (!partner) {
              await trx.rollback();
              apiResponse.message = "Aucun partenaire trouvé pour cette commande";
              return response.status(400).json(apiResponse);
            }

            for (const item of analyzeAskItems) {
              const orderAnalyzeAskItem = orderItemsToPay.find(i => i.analyzeAskItemId === item.id);
              if (orderAnalyzeAskItem) {
                const proposalItem = await QuotationProposalItem.query().useTransaction(trx).where('quotation_proposal_id', proposal.id).where('analyze_ask_item_id', item.id).first();
                if (proposalItem) {
                  await item.merge({
                    isPaid: true,
                    paidAt: DateTime.now(),
                  }).useTransaction(trx).save();

                  await proposalItem.merge({
                    paidAt: DateTime.now(),
                    quantityPaid: orderAnalyzeAskItem.quantity,
                  }).useTransaction(trx).save();
                }
              }
            }

            const isAnalyzePaid = await this.checkAnalyzeAskPaymentStatus(analyzeAskItems, trx);

            const versionId = Number(quotationRequest.currentVersion);
            let versions: QuotationVersion[] = typeof quotationRequest.versions === 'string' ? JSON.parse(quotationRequest.versions as string) : quotationRequest.versions;

            let currentVersion: QuotationVersion | undefined = versions.find(version => version.version_id === versionId);
            if (currentVersion) {
              currentVersion.status = 'completed' as 'completed';
              currentVersion.paid_at = DateTime.now().toISO();
            }

            await partner.merge({
              status: isAnalyzePaid ? 'completed' : 'partially_end',
              completedAt: DateTime.now(),
            }).useTransaction(trx).save();


            analyzeAsk.paymentStatus = isAnalyzePaid ? 'paid' : 'partially_paid';
            await analyzeAsk.useTransaction(trx).save();

            quotationRequest.status = isAnalyzePaid ? 'paid' : 'partially_paid';
            quotationRequest.versions = JSON.stringify(versions);
            await quotationRequest.useTransaction(trx).save();
          }

          await trx.commit();
          apiResponse = {
            success: true,
            message: "Paiement de la commande effectué avec succès",
            result: {
              order: order,
              transaction: transact
            },
          }
          return response.status(201).json(apiResponse);
        } else {
          await trx.rollback();
          apiResponse = {
            success: false,
            message: "Erreur lors du traitement du paiement",
            result: null,
            except: null
          };
          return response.status(200).json(apiResponse);
        }

      } catch (error) {
        await trx.rollback();
        console.error("Error in transaction", error);
        apiResponse = {
          success: false,
          message: "Erreur lors du traitement du paiement",
          result: null,
          except: error.message
        };
        status = 500;
        return response.status(status).json(apiResponse);
      }
    } catch (error) {
      console.log("error in pay order", error);
      apiResponse = {
        success: false,
        message: formatErrorResponse(error),
        result: null,
        except: error.message
      };
      status = 500;
      return response.status(status).json(apiResponse);
    }
  }

  public async AddAppointmentPayment({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {} as ApiResponse;
    let status: number = 201;
    let token = request.header('dotoken');
    try {
      const payload = await request.validate({
        schema: schema.create({
          appointmentId: schema.number([
            rules.exists({ table: 'appointments', column: 'id' })
          ]),
          amount: schema.number(),
        })
      });
      const { appointmentId, amount } = payload;
      const user_id = (await this.verifyJWT(String(token))).user_id as number;

      // Récupérer l'utilisateur et le patient
      const user = await this.getUserById(user_id);
      if (!user) {
        apiResponse.message = "Utilisateur non trouvé";
        return response.status(404).json(apiResponse);
      }

      const patient = await this.getPatientByUserId(user_id);
      if (!patient) {
        apiResponse.message = "Patient non trouvé";
        return response.status(400).json(apiResponse);
      }

      // Récupérer le portefeuille et vérifier le solde
      const wallet = await this.getWalletByUserId(user_id);
      if (!wallet) {
        apiResponse.message = "Portefeuille non trouvé";
        return response.status(404).json(apiResponse);
      }

      const appointment = await Appointment.query().where('id', appointmentId).preload('services').forUpdate().first();
      if (!appointment) {
        apiResponse.message = "Appointment non trouvé";
        return response.status(404).json(apiResponse);
      }
      const services = appointment.services.map(service => service.id);

      const amountPaid = await this.getappointmentAmount(services, appointment.appointmentTypeId);
      if (amountPaid !== 0 && amountPaid !== amount) {
        apiResponse.message = "Echec de paiement, le montant de la transaction est différent du montant du paiement";
        apiResponse.success = false;
        apiResponse.result = null;
      }

      const trx = await Database.transaction();
      try {
        const trxRef = await this.generateUUID();
        const metadata = {
          appointmentId: appointment.id,
          paymentType: 6,
          amount: amountPaid,
          patient: `${patient.last_name} ${patient.first_name}`,
        };

        const trait = new WalletTrait();

        const transact = await Transaction.create({
          userId: user_id,
          beneficiaryId: patient.id,
          paidBySelf: true,
          walletId: wallet.id,
          amount: amount,
          description: "Paiement pour le service Rendez-vous ",
          status: "paid",
          paymentTypeId: 6,
          dateOp: new Date(),
          trxRef: trxRef,
          metadata: metadata,
          balances: {
            last_balance: wallet.balance,
            new_balance: wallet.balance - amount,
          },
        }, { client: trx });

        if (!transact) {
          await trx.rollback();
          apiResponse.message = "Echec de paiement, une erreur est survenue";
          apiResponse.success = false;
          apiResponse.result = null;
          apiResponse.except = transact;
          status = 500;
          return response.status(status).json(apiResponse);
        }

        const debiteWallet = await trait.debiteWallet(wallet, amount, 'payment', transact.id, trx);
        if (!debiteWallet) {
          await trx.rollback();
          apiResponse.message = "Echec de paiement, une erreur est survenue";
          apiResponse.success = false;
          apiResponse.result = null;
          apiResponse.except = debiteWallet;
          status = 500;
          return response.status(status).json(apiResponse);
        }

        // Mise à jour des statuts de commande
        await appointment.merge({
          isPaid: true,
          paidAt: new Date(),
        }).save();
        await trx.commit();
        apiResponse.message = "Paiement effectué avec succès";
        apiResponse.success = true;
        apiResponse.result = transact;
        return response.status(200).json(apiResponse);

      } catch (error) {
        console.log("error", error.message);
        await trx.rollback();
        apiResponse.message = "Echec de paiement, une erreur est survenue";
        apiResponse.success = false;
        apiResponse.result = null;
        apiResponse.except = error.message;
        status = 500;
        return response.status(status).json(apiResponse);
      }


    } catch (error) {
      apiResponse.message = "Echec de paiement, une erreur est survenue";
      apiResponse.success = false;
      apiResponse.result = null;
      apiResponse.except = error.message;
      status = 500;
      return response.status(status).json(apiResponse);
    }
  }

  public async addInsuranceSubscriptionPayment({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null,
    };
    let status: number = 201;
    let token = request.header('dotoken');

    try {
      const payload = await request.validate({
        schema: schema.create({
          patient_insurance_company_id: schema.number([
            rules.exists({ table: 'patient_insurance_companies', column: 'id' })
          ]),
          amount: schema.number(),
          package_id: schema.number([
            rules.exists({ table: 'packages', column: 'id' })
          ]),
        })
      });

      const { patient_insurance_company_id, amount, package_id } = payload;
      const user_id = (await this.verifyJWT(String(token))).user_id as number;

      // Récupérer l'utilisateur et le patient
      const user = await this.getUserById(user_id);
      if (!user) {
        apiResponse.message = "Utilisateur non trouvé";
        return response.status(404).json(apiResponse);
      }

      const patient = await this.getPatientByUserId(user_id);
      if (!patient) {
        apiResponse.message = "Patient non trouvé";
        return response.status(400).json(apiResponse);
      }

      const patientInsuranceCompany = await PatientInsuranceCompany.query()
        .where('id', patient_insurance_company_id)
        .preload('insurance_company')
        .forUpdate()
        .firstOrFail();

      const insurance = patientInsuranceCompany.insurance_company;
      if (!insurance) {
        apiResponse.message = "Compagnie d'assurance non trouvée";
        return response.status(404).json(apiResponse);
      }

      const insurancePackage = await Package.query()
        .where('id', package_id)
        .where('insurance_company_id', insurance.id)
        .firstOrFail();

      const insuranceYear = await this.getActiveYear(insurance.id);
      if (!insuranceYear) {
        apiResponse.message = "Aucune année active trouvée pour cette compagnie";
        return response.status(404).json(apiResponse);
      }

      // Vérification du solde du portefeuille
      const wallet = await this.getWalletByUserId(user_id);
      if (!wallet) {
        apiResponse.message = "Portefeuille non trouvé";
        return response.status(404).json(apiResponse);
      }

      // Vérification si les frais d'adhésion sont requis
      const adhesionRequired = insuranceYear.adhesionFeeRequired;
      const adhesionPrice = Number(insuranceYear.adhesionPrice);

      if (adhesionRequired) {
        // Vérification du montant si frais d'adhésion requis
        if (adhesionPrice !== amount) {
          apiResponse.message = "Le montant de la transaction ne correspond pas aux frais d'adhésion";
          return response.status(400).json(apiResponse);
        }

        if (wallet.balance < amount) {
          apiResponse.message = "Solde insuffisant pour effectuer le paiement";
          return response.status(400).json(apiResponse);
        }
      } else {
        // Si pas de frais d'adhésion, le montant doit être 0
        if (amount !== 0) {
          apiResponse.message = "Aucun frais d'adhésion n'est requis pour cette souscription";
          return response.status(400).json(apiResponse);
        }
      }

      const trx = await Database.transaction();
      try {
        let transaction: Transaction | null = null;

        // Création de la transaction seulement si frais d'adhésion requis
        if (adhesionRequired) {
          const trxRef = await this.generateUUID();
          const metadata = {
            patientInsuranceCompanyId: patientInsuranceCompany.id,
            payment_type_id: 4,
            paymentType: "ADHESION ASSURANCE",
            amount: amount,
            patient: `${patient.last_name} ${patient.first_name}`,
          };
          let description = "Paiement des frais d'adhésion pour la compagnie " + insurance.name;

          const trait = new WalletTrait();

          transaction = await Transaction.create({
            userId: user_id,
            beneficiaryId: patient.id,
            paidBySelf: true,
            walletId: wallet.id,
            amount: adhesionPrice,
            description: description,
            status: "pending",
            paymentTypeId: 4,
            dateOp: new Date(),
            trxRef: trxRef,
            metadata: metadata,
            balances: {
              last_balance: wallet.balance,
              new_balance: wallet.balance - amount,
            },
          }, { client: trx });

          if (!transaction) {
            apiResponse = {
              success: false,
              message: "Échec de création de la transaction",
              result: null,
              except: transaction
            };
            return response.status(500).json(apiResponse);
          }

          const debiteWallet = await trait.debiteWallet(wallet, amount, 'payment', transaction.id, trx);
          if (!debiteWallet) {
            apiResponse = {
              success: false,
              message: "Échec du débit du portefeuille",
              result: null,
              except: debiteWallet
            };
            return response.status(500).json(apiResponse);
          }

          await transaction.merge({ status: 'paid' }).useTransaction(trx).save();
        }

        // Création de la souscription
        let start_date = DateTime.now();
        let end_date = DateTime.now().plus({ months: insurancePackage.validity });
        let feesData = insurancePackage.feesConfig ?
          (typeof insurancePackage.feesConfig === 'string' ?
            JSON.parse(insurancePackage.feesConfig) :
            insurancePackage.feesConfig) : null;
        let tranchesData = insurancePackage.tranchesConfig ?
          (typeof insurancePackage.tranchesConfig === 'string' ?
            JSON.parse(insurancePackage.tranchesConfig) :
            insurancePackage.tranchesConfig) : null;
        let plafondData = insurancePackage.plafondConfig ?
          (typeof insurancePackage.plafondConfig === 'string' ?
            JSON.parse(insurancePackage.plafondConfig) :
            insurancePackage.plafondConfig) : null;

        const newSubscription = await InsuranceCompanySubscription.create({
          patientId: patient.id,
          patientInsuranceCompanyId: patientInsuranceCompany.id,
          insuranceCompanyId: insurance.id,
          packageId: package_id,
          transactionId: transaction?.id || null,
          amountPaid: adhesionRequired ? amount : 0,
          startDate: start_date,
          endDate: end_date,
          status: 'active',
          validatedAt: DateTime.now(),
          fees: JSON.stringify(feesData),
          tranches: JSON.stringify(tranchesData),
          plafond: JSON.stringify(plafondData),
          subscriptionType: insurancePackage.type,

        }, { client: trx });

        if (!newSubscription) {
          apiResponse = {
            success: false,
            message: "Échec de création de la souscription",
            result: null,
            except: newSubscription
          };
          return response.status(500).json(apiResponse);
        }

        // Mise à jour du statut du patient
        let probationEnd = DateTime.now().plus({ months: Number(insurance.probationDuration) });
        await patientInsuranceCompany.merge({
          status: 'activated',
          probation_start_at: start_date,
          probation_end_at: probationEnd,
          is_active: true,
        }).useTransaction(trx).save();

        const eventData: NewSubscriptionData = {
          subscription_id: newSubscription.id,
          insurance_year_id: insuranceYear.id,
          package_id: package_id,
          patient_id: patient.id,
        }

        const nats = await NatService;
        const coverageResponse = await nats.request('subscription.created', eventData);
        if (!coverageResponse.success) {
          await trx.rollback();
          apiResponse = {
            success: false,
            message: "Échec de la création de la couverture",
            result: null,
            except: coverageResponse
          };
          return response.status(500).json(apiResponse);
        }

        await trx.commit();

        apiResponse = {
          success: true,
          message: adhesionRequired ? "Paiement et souscription effectués avec succès" : "Souscription effectuée avec succès",
          result: {
            subscription: newSubscription,
            transaction: transaction || { message: "Aucune transaction (frais d'adhésion non requis)" },
          },
        };

        return response.status(status).json(apiResponse);

      } catch (error) {
        await trx.rollback();
        console.error("Erreur lors de la souscription:", error);

        apiResponse = {
          success: false,
          message: "Échec de la souscription",
          result: null,
          except: error.message
        };
        status = 500;
        return response.status(status).json(apiResponse);
      }

    } catch (error) {
      console.error("Erreur dans addInsuranceSubscriptionPayment:", error);
      apiResponse = {
        success: false,
        message: "Échec de la souscription, une erreur est survenue",
        result: null,
        except: error.message
      };
      status = 500;
      return response.status(status).json(apiResponse);
    }
  }


  public async payInsuranceFees({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = { success: false, message: '', result: null, except: null };
    let status = 201;
    let token = request.header('dotoken');

    try {
      const payload = await request.validate({
        schema: schema.create({
          patient_insurance_company_id: schema.number([rules.exists({ table: 'patient_insurance_companies', column: 'id' })]),
          amount: schema.number(),
          payment_mode: schema.enum(['monthly', 'yearly']),
          total_months: schema.number.optional(),
          paid_by_tranche: schema.boolean(),
          beneficiary_id: schema.number.optional(),
        }),
      });

      const { patient_insurance_company_id, amount, payment_mode, total_months, paid_by_tranche, beneficiary_id } = payload;
      const user_id = (await this.verifyJWT(String(token))).user_id as number;

      // Récupérer l'utilisateur, le patient et le portefeuille en une seule opération
      const [user, patient, wallet] = await Promise.all([
        this.getUserById(user_id),
        this.getPatientByUserId(user_id),
        this.getWalletByUserId(user_id),
      ]);

      if (!user) return response.status(404).json({ ...apiResponse, message: "Utilisateur non trouvé" });
      if (!patient) return response.status(400).json({ ...apiResponse, message: "Patient non trouvé" });
      if (!wallet) return response.status(404).json({ ...apiResponse, message: "Portefeuille non trouvé" });

      if (wallet.balance < amount) {
        return response.status(400).json({ ...apiResponse, message: "Solde insuffisant pour la souscription" });
      }

      let query = PatientInsuranceCompany.query().preload('insurance_company').forUpdate();
      let patientId = patient.id;
      if (beneficiary_id) {
        const beneficiary = await InsuranceCompanyBeneficiary.query().where('id', beneficiary_id).first();
        if (!beneficiary) {
          return response.status(400).json({ ...apiResponse, message: "Le bénéficiaire n'existe pas" });
        }
        if (!beneficiary.patientId && beneficiary.patientId === null) {
          return response.status(400).json({ ...apiResponse, message: "Le bénéficiaire ne dispose pas encore d'un compte patient ou n'est pas encore validé" });
        }
        let beneficiary_insurance_id = beneficiary.insuranceCompanyId;
        query = PatientInsuranceCompany.query().where('insurance_company_id', beneficiary_insurance_id).where('patient_id', beneficiary.patientId).preload('insurance_company').forUpdate();
        patientId = beneficiary.patientId;
      } else {
        query = query.where('id', patient_insurance_company_id).where('patient_id', patient.id).preload('insurance_company').forUpdate();
      }

      // Récupérer les informations d'assurance et la souscription active
      const patientInsuranceCompany = await query.first();

      if (!patientInsuranceCompany) return response.status(404).json({ ...apiResponse, message: "PatientInsuranceCompany non trouvé" });
      const insurance = patientInsuranceCompany.insurance_company;
      if (!insurance) return response.status(404).json({ ...apiResponse, message: "Compagnie d'assurance non trouvée" });

      const activeSubscription = await this.getPatientActiveInsuranceSubscription(patient_insurance_company_id);
      if (!activeSubscription) return response.status(400).json({ ...apiResponse, message: "Aucune souscription active trouvée" });

      const insurancePackage = activeSubscription.package;
      if (!insurancePackage) return response.status(400).json({ ...apiResponse, message: "Aucun package trouvé" });

      // Gestion de la transaction
      const trx = await Database.transaction();
      try {
        const trxRef = await this.generateUUID();
        const description = `Paiement des frais de cotisation pour la compagnie ${insurance.name}`;
        const metadata = {
          patientInsuranceCompanyId: patientInsuranceCompany.id,
          payment_type_id: 5,
          paymentType: "COTISATION ASSURANCE",
          amount,
          patient: `${patient.last_name} ${patient.first_name}`
        };
        const trait = new WalletTrait();

        const transaction = await Transaction.create({
          userId: user_id,
          beneficiaryId: patient.id,
          paidBySelf: true,
          walletId: wallet.id,
          amount,
          description,
          status: "paid",
          paymentTypeId: 4,
          dateOp: new Date(),
          trxRef,
          metadata,
          balances: { last_balance: wallet.balance, new_balance: wallet.balance - amount },
        }, { client: trx });

        if (!transaction) {
          await trx.rollback();
          console.log("error in payInsuranceFees transaction", transaction);

          apiResponse.except = transaction;
          return response.status(500).json({ ...apiResponse, message: "Échec de la création de la transaction" });
        }

        const debiteWallet = await trait.debiteWallet(wallet, amount, 'payment', transaction.id, trx);
        if (!debiteWallet) {
          console.log("error in payInsuranceFees debiteWallet", debiteWallet);
          await trx.rollback();
          apiResponse.except = debiteWallet;
          return response.status(500).json({ ...apiResponse, message: "Échec du débit du portefeuille" });
        }

        const insuranceWallet = await this.getInsuranceCompanyWallet(Number(patientInsuranceCompany.agencyId));
        if (!insuranceWallet) {
          await trx.rollback();
          return response.status(500).json({ ...apiResponse, message: "Échec de la récupération du portefeuille de l'agence" });
        }
        const crediteWallet = await trait.crediteWallet(insuranceWallet, transaction.amount, 'payment', transaction.id, trx);
        if (!crediteWallet) {
          console.log("error in payInsuranceFees crediteWallet", crediteWallet);
          await trx.rollback();
          apiResponse.except = crediteWallet;
          return response.status(500).json({ ...apiResponse, message: "Échec du crédit du portefeuille de l'agence" });
        }

        const addFees = await this.addInsuranceFees(
          patientId,
          patientInsuranceCompany,
          insurance,
          activeSubscription,
          insurancePackage,
          transaction,
          transaction.amount,
          payment_mode as 'monthly' | 'yearly',
          paid_by_tranche,
          Number(total_months),
          trx,
          apiResponse
        )

        if (!addFees.success) {
          await trx.rollback();
          apiResponse.except = addFees;
          console.log("error in addInsuranceFees", addFees);
          return response.status(500).json({ ...apiResponse, message: "Échec du paiement" });
        }
        let insuranceFee = addFees.result;
        await trx.commit();
        apiResponse.success = true;
        apiResponse.message = "Paiement réussi";
        apiResponse.result = {
          insuranceFee,
          transaction,
        };
        return response.status(status).json(apiResponse);
      } catch (error) {
        await trx.rollback();
        console.error("Erreur dans payInsuranceFees addInsuranceFees:", error);
        return response.status(500).json({ ...apiResponse, message: "Erreur lors du paiement", except: error.message });
      }
    } catch (error) {
      console.error("Erreur dans payInsuranceFees:", error.message);
      return response.status(500).json({ ...apiResponse, message: "Erreur de validation", except: error.message });
    }
  }

  public async addInsuranceFees(
    patientId: number,
    patientInsuranceCompany: PatientInsuranceCompany,
    insurance: InsuranceCompany,
    activeSubscription: InsuranceCompanySubscription,
    insurancePackage: Package,
    transaction: Transaction,
    amount: number,
    payment_mode: 'monthly' | 'yearly',
    paid_by_tranche: boolean,
    total_months: number,
    trx: TransactionClientContract,
    apiResponse: ApiResponse
  ): Promise<ApiResponse> {
    // Recherche de la dernière cotisation existante
    let insuranceFee = await InsuranceCompanyFee.query()
      .where('patient_insurance_company_id', patientInsuranceCompany.id)
      .where('insurance_company_subscription_id', activeSubscription.id)
      .orderBy('created_at', 'desc')
      .first();

    const reference = Math.floor(********00 + Math.random() * ********00).toString();

    let adhrentType = patientInsuranceCompany.type_client;
    let subscription_amount = activeSubscription.fees?.principal_fee || 0 as number;
    if (adhrentType === 'PAC') {
      let beneficiary = await InsuranceCompanyBeneficiary.query().where('patient_id', patientId).first();
      if (beneficiary) {
        subscription_amount = beneficiary.type === 'child' ? activeSubscription.fees?.child_fee || 0 : activeSubscription.fees?.partner_fee || 0;
      }
    }

    if (!insuranceFee) {
      // Cas 1 : Création d'une nouvelle cotisation (premier paiement)
      insuranceFee = new InsuranceCompanyFee();
      Object.assign(insuranceFee, {
        patient_id: patientId,
        patient_insurance_company_id: patientInsuranceCompany.id,
        insurance_company_id: insurance.id,
        agencyId: patientInsuranceCompany.agencyId || null,
        insurance_company_subscription_id: activeSubscription.id,
        packageId: insurancePackage.id,
        transactionId: transaction.id,
        reference,
        token: transaction.trxRef,
        amountPaid: amount,
        is_valide: true,
        type_cotisation: payment_mode,
        paidByTranche: paid_by_tranche,
        paidAt: DateTime.now(),
        startAt: DateTime.now(),
        total_month: payment_mode === 'monthly' ? total_months || 12 : 12,
        endAt: payment_mode === 'monthly' ? DateTime.now().plus({ months: total_months || 12 }) : DateTime.now().plus({ years: 1 }),
        status: paid_by_tranche ? InsuranceFeeStatus.Pending : InsuranceFeeStatus.Completed,
      });

      // Gestion des tranches si le paiement est par tranche
      if (paid_by_tranche) {
        const tranche_configs = activeSubscription.tranches;
        if (tranche_configs) {
          const firstPercentage = tranche_configs.percentages[0];
          const tranches = [{
            id: 1,
            amount: insurancePackage.price * firstPercentage / 100,
            method: 'wallet',
            status: 'paid',
            paid_at: new Date(),
            reference,
            token: transaction.trxRef,
            transaction_id: transaction.id,
          }];
          insuranceFee.tranches = JSON.stringify(tranches);
          let amountPaid = subscription_amount * firstPercentage / 100;
          let newTotalAmountPaid = Number(activeSubscription.totalAmountPaid) + Number(amountPaid);
          activeSubscription.totalAmountPaid = Number(newTotalAmountPaid.toFixed(2));

          await activeSubscription.useTransaction(trx).save();
        }
      }

      // Sauvegarde de la nouvelle cotisation
      await insuranceFee.useTransaction(trx).save();

      // Mise à jour du next_deadline pour les paiements mensuels ou annuels
      if (!paid_by_tranche) {
        let nextDeadline;
        if (payment_mode === 'monthly') {
          nextDeadline = DateTime.now().plus({ months: total_months });
        } else if (payment_mode === 'yearly') {
          nextDeadline = DateTime.now().plus({ years: 1 });
        }
        let amountPaid = amount;
        let newTotalAmountPaid = Number(activeSubscription.totalAmountPaid) + Number(amountPaid);
        activeSubscription.totalAmountPaid = Number(newTotalAmountPaid.toFixed(2));
        activeSubscription.isUpToDate = true;
        activeSubscription.nextDeadline = nextDeadline;
        await activeSubscription.useTransaction(trx).save();
      }

      apiResponse = {
        success: true,
        message: "",
        result: insuranceFee
      }

    } else {
      // Cas 2 : Mise à jour d'une cotisation existante
      if (paid_by_tranche) {
        // Paiement par tranche
        insuranceFee.amountPaid += amount;
        insuranceFee.amountRemaining = insurancePackage.price - insuranceFee.amountPaid;
        insuranceFee.paidAt = DateTime.now();

        let tranches: PaymentTranche[] = [];

        if (typeof insuranceFee.tranches === 'string') {
          try {
            tranches = JSON.parse(insuranceFee.tranches) as PaymentTranche[];
          } catch (error) {
            console.error("Erreur lors du parsing des tranches :", error);
          }
        } else if (Array.isArray(insuranceFee.tranches)) {
          tranches = insuranceFee.tranches as PaymentTranche[];
        }
        const tranche_configs = insurancePackage.tranchesConfig;
        if (tranche_configs) {
          const nextIndex = tranches.length + 1;
          const percentage = tranche_configs.percentages[nextIndex - 1] || 100;
          tranches.push({
            id: nextIndex,
            amount: subscription_amount * percentage / 100,
            method: 'wallet',
            status: 'paid',
            paid_at: new Date(),
            reference,
            token: transaction.trxRef,
            transaction_id: transaction.id,
          });
          insuranceFee.tranches = JSON.stringify(tranches);
          let amountPaid = subscription_amount * percentage / 100;
          let newTotalAmountPaid = Number(activeSubscription.totalAmountPaid) + Number(amountPaid);
          activeSubscription.totalAmountPaid = Number(newTotalAmountPaid.toFixed(2));

          // Si toutes les tranches sont payées, marquer la cotisation comme complète
          if (nextIndex >= tranche_configs.percentages.length) {
            insuranceFee.status = InsuranceFeeStatus.Completed;
            insuranceFee.startAt = DateTime.now();
            insuranceFee.endAt = DateTime.now().plus({ years: 1 });

            activeSubscription.isUpToDate = true;
            activeSubscription.nextDeadline = DateTime.now().plus({ years: 1 });
          }
          await activeSubscription.useTransaction(trx).save();
        }
        await insuranceFee.useTransaction(trx).save();

        apiResponse = {
          success: true,
          message: "",
          result: insuranceFee
        }

      } else {
        // Paiement mensuel ou annuel
        let start_at = DateTime.fromISO(insuranceFee.endAt.toString()).plus({ days: 1 });
        let end_at = DateTime.fromISO(start_at.toString()).plus({ months: total_months });

        const newFees = await InsuranceCompanyFee.create({
          patient_id: patientId,
          patient_insurance_company_id: patientInsuranceCompany.id,
          insurance_company_id: insurance.id,
          agencyId: patientInsuranceCompany.agencyId || null,
          insurance_company_subscription_id: activeSubscription.id,
          packageId: insurancePackage.id,
          transactionId: transaction.id,
          reference,
          token: transaction.trxRef,
          amountPaid: amount,
          is_valide: true,
          type_cotisation: payment_mode,
          paidByTranche: false,
          paidAt: DateTime.now(),
          total_month: total_months,
          startAt: start_at,
          endAt: end_at,
          status: InsuranceFeeStatus.Completed,
        }, { client: trx });

        // Mise à jour du next_deadline pour les paiements mensuels ou annuels
        if (payment_mode === 'monthly') {
          activeSubscription.nextDeadline = end_at;
        } else if (payment_mode === 'yearly') {
          activeSubscription.nextDeadline = DateTime.now().plus({ years: 1 });
        }

        let newTotalAmountPaid = Number(activeSubscription.totalAmountPaid) + Number(amount);
        activeSubscription.totalAmountPaid = Number(newTotalAmountPaid.toFixed(2));
        activeSubscription.isUpToDate = true;
        await activeSubscription.useTransaction(trx).save();

        if (!newFees) {
          apiResponse.success = false;
          apiResponse.message = "Erreur lors de la création de la cotisation";
          apiResponse.except = newFees;
        }

        apiResponse = {
          success: true,
          message: "",
          result: insuranceFee
        }
      }

    }
    return apiResponse;
  }


  public async payQrcodeForBeneficiary({ request, response }: HttpContextContract) {
    let apiResponse: ApiResponse = {
      success: false,
      message: '',
      result: null as any,
      except: null as any,
    };

    const token = request.header('dotoken');

    if (!token) {
      apiResponse.message = "Token d'authentification manquant";
      return response.status(401).json(apiResponse);
    }

    try {
      // Valider et extraire les données du payload
      const payload = await request.validate({
        schema: schema.create({
          beneficiary_id: schema.number(),
          code_parrainage: schema.string.optional(),
        }),
      });

      const { beneficiary_id, code_parrainage } = payload;

      // Vérifier l'authenticité du token et récupérer l'utilisateur
      const userPayload = await this.verifyJWT(String(token));
      if (!userPayload || !userPayload.user_id) {
        apiResponse.message = "Token invalide ou expiré";
        return response.status(401).json(apiResponse);
      }

      const userId = userPayload.user_id;

      // Vérifier si l'utilisateur est un parent assuré
      const parent = await this.getPatientByUserId(userId);
      if (!parent) {
        apiResponse.message = "Utilisateur non trouvé ou n'est pas un parent assuré";
        return response.status(404).json(apiResponse);
      }

      // Récupérer le portefeuille du parent et vérifier le solde
      const walletValidation = await this.validateWalletAndBalance(parent.user_id, 'QRCODE');
      if (!walletValidation.success) {
        apiResponse.message = walletValidation.message;
        return response.status(walletValidation.statusCode).json(apiResponse);
      }

      const wallet = walletValidation.wallet;
      const qrcodeFees = Number(walletValidation.fees);

      // Vérifier l'existence du bénéficiaire
      const beneficiary = await InsuranceCompanyBeneficiary.query().where('id', beneficiary_id).first();

      if (!beneficiary) {
        apiResponse.message = "Aucun compte n'existe pour cette assurance";
        return response.status(404).json(apiResponse);
      }

      // Sélectionner une carte disponible
      const card = await Card.query()
        .where('is_paid', false)
        .andWhere('is_used', false)
        .andWhere('status', 'new')
        .andWhere('type', 'online') // Ou 'physical' selon vos besoins
        .forUpdate()
        .first();

      if (!card) {
        apiResponse.message = "Aucune carte disponible";
        return response.status(400).json(apiResponse);
      }

      // Démarrer une transaction
      const trx = await Database.transaction();

      try {

        // Vérifier si un carnet est déjà lié à cette carte
        const existingCarnet = await Code.query().where('card_id', card.id).first();
        if (existingCarnet) {
          apiResponse.message = "Carnet déjà en cours d'utilisation.";
          return response.status(400).json(apiResponse);
        }

        // Créer le compte utilisateur et le patient associé
        const accountCreationResult = await this.createBeneficiaryAccount(beneficiary, parent, trx);

        if (!accountCreationResult.success) {
          await trx.rollback();
          apiResponse.message = accountCreationResult.message;
          return response.status(500).json(apiResponse);
        }

        const { user, patient } = accountCreationResult;

        if (!user || !patient) {
          await trx.rollback();
          apiResponse.message = 'Erreur lors de la création du compte';
          return response.status(500).json(apiResponse);
        }

        // Créer la transaction de paiement
        const paymentResult = await this.createPaymentTransaction(
          parent,
          patient,
          beneficiary,
          wallet,
          card,
          qrcodeFees,
          trx
        );

        if (!paymentResult.success) {
          await trx.rollback();
          apiResponse.message = paymentResult.message;
          return response.status(500).json(apiResponse);
        }

        const { transact, newCode } = paymentResult;

        // Gestion du parrainage (optionnel)
        if (code_parrainage) {
          const parrainage = await this.addQrcodeParrainage(
            trx,
            code_parrainage,
            parent.user_id,
            patient.last_name,
            patient.first_name,

          );

          if (!parrainage.success) {
            await trx.rollback();
            apiResponse.message = parrainage.message;
            return response.status(500).json(apiResponse);
          }
        }

        // Mettre à jour le stock de la carte
        await this.updateCardOrderStock(card, trx);

        // Valider la transaction
        await trx.commit();

        // Réponse réussie
        apiResponse.success = true;
        apiResponse.message = "Paiement de la carte effectué avec succès pour le bénéficiaire.";
        apiResponse.result = {
          beneficiary,
          patient,
          carnet: newCode,
          user,
          transact,
        };

        return response.status(200).json(apiResponse);
      } catch (error) {
        await trx.rollback();
        console.error("Error in payQrcodeForBeneficiary:", error.message);
        apiResponse.message = formatErrorResponse(error);
        apiResponse.except = error.message;
        return response.status(500).json(apiResponse);
      }
    } catch (error) {
      console.error("Error in payQrcodeForBeneficiary:", error.message);
      apiResponse.message = formatErrorResponse(error);
      apiResponse.except = error.message;
      return response.status(500).json(apiResponse);
    }
  }


  // Fonction pour créer le compte utilisateur et le patient associé au bénéficiaire
  private async createBeneficiaryAccount(
    beneficiary: InsuranceCompanyBeneficiary,
    parent: Patient,
    trx?: TransactionClientContract
  ): Promise<{ success: boolean; message: string; user?: User; patient?: Patient }> {
    const roleId = 6;
    const codeP = await this.generateCodeParrainage(8);
    const username = `${beneficiary.lastName.toUpperCase()} ${beneficiary.firstName.toLowerCase()}`;
    const identificationNumber = beneficiary.phone ?? Math.floor(******** + Math.random() * ********);

    // Créer l'utilisateur
    const user = await User.create(
      {
        username,
        phone: beneficiary.phone ?? null,
        email: beneficiary.email ?? null,
        countryId: Number(parent.country_id),
        roleId,
        status: UserStatus.Actived,
        codeParrainage: codeP,
        identificationNumber: identificationNumber.toString(),
        parrainage: JSON.stringify({
          create_account: 0,
          active_qrcode: 0,
          adhesion_fees: 0,
          plan: 1,
          activeMoney: false,
        }),
      },
      trx ? { client: trx } : {}
    );

    if (!user) {
      return { success: false, message: "Echec lors de l'ajout de l'utilisateur." };
    }

    // Créer le patient
    const codePatient = await this.generateToken();
    const channel = await this.generateChannel(username);
    const patient = await Patient.create(
      {
        user_id: user.id,
        last_name: beneficiary.lastName,
        first_name: beneficiary.firstName,
        email: beneficiary.email ? String(beneficiary.email) : null,
        phone: beneficiary.phone ?? null,
        gender: beneficiary.gender,
        birthday_year: beneficiary.birthdayYear,
        birthday_month: beneficiary.birthdayMonth,
        birthday_day: beneficiary.birthdayDay,
        country_id: parent.country_id,
        status: PatientStatus.Activated,
        code: codePatient.toString(),
        channel: channel
      },
      trx ? { client: trx } : {}
    );

    if (!patient) {
      return { success: false, message: "Echec lors de l'ajout du compte patient." };
    }

    // Créer le wallet
    const walletCode = await this.generateWalletCode();
    await Wallet.create(
      {
        userId: user.id,
        ownerType: 'patient',
        ownerId: patient.id,
        libelle: "DO WALLET",
        typeWalletId: 2,
        code: walletCode,
      },
      trx ? { client: trx } : {}
    );

    return { success: true, message: "", user, patient };
  }

  // Fonction pour créer la transaction de paiement
  public async createPaymentTransaction(parent: Patient, patient: Patient, beneficiary: InsuranceCompanyBeneficiary, wallet: Wallet, card: Card, qrcodeFees: number, trx: TransactionClientContract): Promise<{ success: boolean; message: string; transact?: any; newCode?: any }> {
    const trxRef = await this.generateUUID();
    const description = "Paiement de la carte QRCODE pour un bénéficiaire";

    const metadata = {
      cardId: card.id,
      paymentType: "QRCODE",
      amount: qrcodeFees,
      patient: `${patient.last_name} ${patient.first_name}`,
      beneficiaryId: patient.id,
    };

    // Créer la transaction
    const transact = await Transaction.create(
      {
        userId: parent.user_id,
        beneficiaryId: patient.id,
        paidBySelf: false,
        walletId: wallet.id,
        amount: qrcodeFees,
        description: description,
        status: "pending",
        paymentTypeId: 1,
        dateOp: new Date(),
        trxRef: trxRef,
        metadata: metadata,
        balances: {
          last_balance: wallet.balance,
          new_balance: wallet.balance - qrcodeFees,
        },
      },
      { client: trx }
    );

    if (!transact) {
      return { success: false, message: "Erreur lors de la création de la transaction" };
    }

    // Débiter le wallet
    const trait = new WalletTrait();
    const debite = await trait.debiteWallet(wallet, qrcodeFees, "payment", transact.id, trx);

    if (!debite.success) {
      return { success: false, message: debite.message };
    }

    // Créer le code associé à la carte
    const validity = 365;
    const newCode = await Code.create(
      {
        patientId: patient.id,
        cardId: card.id,
        validity: validity.toString(),
        activatedAt: DateTime.now().toISODate(),
        expiredAt: DateTime.now().plus({ days: validity }).toISODate(),
        onlinePaid: 1,
      },
      { client: trx }
    );

    if (!newCode) {
      return { success: false, message: "Erreur lors de la création du code" };
    }

    // Mettre à jour la carte et le patient
    await card.merge({ isUsed: true, isPaid: true, status: 'used' }).useTransaction(trx).save();
    await patient.merge({ carnet_is_active: true }).useTransaction(trx).save();
    // Mettre à jour le bénéficiaire
    await beneficiary.useTransaction(trx).merge({
      patientId: patient.id,
    }).save();

    // Mettre à jour la transaction comme payée
    transact.status = "paid";
    await transact.useTransaction(trx).save();

    return { success: true, message: "", transact, newCode };
  }




}
