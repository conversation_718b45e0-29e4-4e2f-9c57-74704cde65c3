import { DateTime } from 'luxon'
import Hash from '@ioc:Adonis/Core/Hash'
import { column, beforeSave, BaseModel, belongsTo, BelongsTo, hasMany, HasMany, hasOne, HasOne,  } from '@ioc:Adonis/Lucid/Orm'
import Role from './Role'
import Country from './Country'
import Language from './Language'
import UserConnection from './UserConnection'
import Wallet from './Wallet'
import Patient from './Patient'
import { UserStatus } from 'App/Controllers/Utils/models'


export default class User extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column({ columnName: 'username' })
  public username: string

  @column({ columnName: 'phone' })
  public phone: string | null

  @column({ columnName: 'email' })
  public email: string | null


  @column({ columnName: 'identification_number' })
  public identificationNumber: string | null

  @column({ columnName: 'creator_id' })
  public creatorId: number

  @column({ columnName: 'language_id' })
  public languageId: number

  @column({ columnName: 'country_id' })
  public countryId: number

  @column({ columnName: 'role_id' })
  public roleId: number

  @column({ columnName: 'status' })
  public status: UserStatus

  @column({ columnName: 'firebase_instance_id' })
  public firebaseInstanceId: string

  @column({ columnName: 'online' })
  public online: number;

  @column({ columnName: 'profileIMG' })
  public profileIMG: string

  @column.dateTime({ columnName: 'email_verified_at' })
  public emailVerifiedAt: DateTime | null

  @column.dateTime({ columnName: 'phone_verified_at' })
  public phoneVerifiedAt: DateTime | null

  @column.dateTime({ columnName: 'activated_at' })
  public activatedAt: DateTime

  @column.dateTime({ columnName: 'blocked_at' })
  public blockedAt: DateTime

  @column({ serializeAs: null })
  public password: string

  @column({columnName: 'token'})
  public token: string | null;

  @column({columnName: 'code_parrainage'})
  public codeParrainage: string

  @column({columnName: 'parrainage'})
  public parrainage: any | null

  @column()
  public rememberMeToken: string | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @beforeSave()
  public static async hashPassword (user: User) {
    if (user.$dirty.password) {
      user.password = await Hash.make(user.password)
    }
  }

  @belongsTo(() => Role,{
    foreignKey: 'roleId',
    localKey: 'id'
  })
  public role: BelongsTo<typeof Role>

  @belongsTo(() => Country,{
    foreignKey: 'countryId',
    localKey: 'id'
  })
  public country: BelongsTo<typeof Country>

  @belongsTo(() => Language,{
    foreignKey: 'languageId',
    localKey: 'id'
  })
  public language: BelongsTo<typeof Language>

  @belongsTo(() => User,{
    foreignKey: 'creatorId',
    localKey: 'id'
  })
  public creator: BelongsTo<typeof User>

  @hasMany(() => UserConnection,{
    foreignKey: 'userId',
    localKey: 'id'
  })
  public userHistories: HasMany<typeof UserConnection>

  @hasOne(() => Patient, {
    foreignKey: 'userId',
    localKey: 'id'
  })
  public patient: HasOne<typeof Patient>

  @hasOne(() => Wallet, {
    foreignKey: 'userId',
    localKey: 'id'
  })
  public wallet: HasOne<typeof Wallet>

}
